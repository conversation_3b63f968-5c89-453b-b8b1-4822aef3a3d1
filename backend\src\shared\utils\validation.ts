import Joi from 'joi';

/**
 * Common validation schemas for the application
 */

/**
 * User registration validation schema
 */
export const userRegistrationSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required',
    }),
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .required()
    .messages({
      'string.alphanum': 'Username must contain only alphanumeric characters',
      'string.min': 'Username must be at least 3 characters long',
      'string.max': 'Username must not exceed 30 characters',
      'any.required': 'Username is required',
    }),
  password: Joi.string()
    .min(8)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\\$%\\^&\\*])'))
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character',
      'any.required': 'Password is required',
    }),
  firstName: Joi.string()
    .min(1)
    .max(50)
    .optional()
    .allow('')
    .messages({
      'string.min': 'First name must be at least 1 character long',
      'string.max': 'First name must not exceed 50 characters',
    }),
  lastName: Joi.string()
    .min(1)
    .max(50)
    .optional()
    .allow('')
    .messages({
      'string.min': 'Last name must be at least 1 character long',
      'string.max': 'Last name must not exceed 50 characters',
    }),
  language: Joi.string()
    .valid('en', 'ar')
    .default('en')
    .messages({
      'any.only': 'Language must be either "en" or "ar"',
    }),
});

/**
 * User login validation schema
 */
export const userLoginSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required',
    }),
  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required',
    }),
});

/**
 * Task creation validation schema
 */
export const taskCreationSchema = Joi.object({
  title: Joi.string()
    .min(1)
    .max(200)
    .required()
    .messages({
      'string.min': 'Task title must be at least 1 character long',
      'string.max': 'Task title must not exceed 200 characters',
      'any.required': 'Task title is required',
    }),
  description: Joi.string()
    .max(1000)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Task description must not exceed 1000 characters',
    }),
  priority: Joi.string()
    .valid('LOW', 'MEDIUM', 'HIGH', 'URGENT')
    .default('MEDIUM')
    .messages({
      'any.only': 'Priority must be one of: LOW, MEDIUM, HIGH, URGENT',
    }),
  dueDate: Joi.date()
    .iso()
    .min('now')
    .optional()
    .messages({
      'date.min': 'Due date must be in the future',
      'date.format': 'Due date must be a valid ISO date',
    }),
});

/**
 * Task update validation schema
 */
export const taskUpdateSchema = Joi.object({
  title: Joi.string()
    .min(1)
    .max(200)
    .optional()
    .messages({
      'string.min': 'Task title must be at least 1 character long',
      'string.max': 'Task title must not exceed 200 characters',
    }),
  description: Joi.string()
    .max(1000)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Task description must not exceed 1000 characters',
    }),
  isCompleted: Joi.boolean()
    .optional()
    .messages({
      'boolean.base': 'isCompleted must be a boolean value',
    }),
  priority: Joi.string()
    .valid('LOW', 'MEDIUM', 'HIGH', 'URGENT')
    .optional()
    .messages({
      'any.only': 'Priority must be one of: LOW, MEDIUM, HIGH, URGENT',
    }),
  dueDate: Joi.date()
    .iso()
    .optional()
    .allow(null)
    .messages({
      'date.format': 'Due date must be a valid ISO date',
    }),
});

/**
 * Task query parameters validation schema
 */
export const taskQuerySchema = Joi.object({
  isCompleted: Joi.boolean()
    .optional()
    .messages({
      'boolean.base': 'isCompleted must be a boolean value',
    }),
  priority: Joi.string()
    .valid('LOW', 'MEDIUM', 'HIGH', 'URGENT')
    .optional()
    .messages({
      'any.only': 'Priority must be one of: LOW, MEDIUM, HIGH, URGENT',
    }),
  search: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Search query must not exceed 100 characters',
    }),
  sortBy: Joi.string()
    .valid('createdAt', 'updatedAt', 'dueDate', 'priority', 'title')
    .default('createdAt')
    .messages({
      'any.only': 'sortBy must be one of: createdAt, updatedAt, dueDate, priority, title',
    }),
  sortOrder: Joi.string()
    .valid('asc', 'desc')
    .default('desc')
    .messages({
      'any.only': 'sortOrder must be either "asc" or "desc"',
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(20)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit must not exceed 100',
    }),
  offset: Joi.number()
    .integer()
    .min(0)
    .default(0)
    .messages({
      'number.base': 'Offset must be a number',
      'number.integer': 'Offset must be an integer',
      'number.min': 'Offset must be at least 0',
    }),
});

/**
 * ID parameter validation schema
 */
export const idParamSchema = Joi.object({
  id: Joi.string()
    .required()
    .messages({
      'any.required': 'ID is required',
      'string.base': 'ID must be a string',
    }),
});

/**
 * Validates data against a Joi schema
 */
export const validateData = <T>(schema: Joi.ObjectSchema, data: any): T => {
  const { error, value } = schema.validate(data, {
    abortEarly: false,
    stripUnknown: true,
  });

  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join(', ');
    throw new Error(`Validation error: ${errorMessage}`);
  }

  return value as T;
};
