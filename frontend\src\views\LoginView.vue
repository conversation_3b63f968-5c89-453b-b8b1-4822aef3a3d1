<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          {{ t('auth.loginTitle') }}
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          {{ t('auth.loginSubtitle') }}
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="email" class="sr-only">{{ t('auth.email') }}</label>
            <input
              id="email"
              v-model="form.email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="form-input rounded-t-md"
              :placeholder="t('auth.email')"
            />
          </div>
          <div>
            <label for="password" class="sr-only">{{ t('auth.password') }}</label>
            <input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              autocomplete="current-password"
              required
              class="form-input rounded-b-md"
              :placeholder="t('auth.password')"
            />
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input
              id="remember-me"
              v-model="form.rememberMe"
              name="remember-me"
              type="checkbox"
              class="form-checkbox"
            />
            <label for="remember-me" class="ml-2 block text-sm text-gray-900">
              {{ t('auth.rememberMe') }}
            </label>
          </div>

          <div class="text-sm">
            <a href="#" class="font-medium text-primary-600 hover:text-primary-500">
              {{ t('auth.forgotPassword') }}
            </a>
          </div>
        </div>

        <div>
          <button
            type="submit"
            :disabled="authStore.isLoading"
            class="btn-primary w-full"
          >
            <span v-if="authStore.isLoading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ t('common.loading') }}
            </span>
            <span v-else>{{ t('auth.login') }}</span>
          </button>
        </div>

        <div class="text-center">
          <p class="text-sm text-gray-600">
            {{ t('auth.dontHaveAccount') }}
            <router-link to="/register" class="font-medium text-primary-600 hover:text-primary-500">
              {{ t('auth.register') }}
            </router-link>
          </p>
        </div>

        <!-- Language Switcher -->
        <div class="flex justify-center space-x-4">
          <button
            type="button"
            @click="uiStore.setLanguage('en')"
            :class="[
              'px-3 py-1 rounded text-sm',
              uiStore.language === 'en' 
                ? 'bg-primary-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            ]"
          >
            English
          </button>
          <button
            type="button"
            @click="uiStore.setLanguage('ar')"
            :class="[
              'px-3 py-1 rounded text-sm',
              uiStore.language === 'ar' 
                ? 'bg-primary-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            ]"
          >
            العربية
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useAuthStore } from '@/stores/auth';
import { useUIStore } from '@/stores/ui';

const router = useRouter();
const { t } = useI18n();
const authStore = useAuthStore();
const uiStore = useUIStore();

const form = reactive({
  email: '',
  password: '',
  rememberMe: false,
});

async function handleLogin() {
  try {
    await authStore.login({
      email: form.email,
      password: form.password,
    });
    
    uiStore.showSuccess(t('auth.loginSuccess'));
    router.push('/dashboard');
  } catch (error: any) {
    uiStore.showError(t('auth.invalidCredentials'), error.message);
  }
}
</script>
