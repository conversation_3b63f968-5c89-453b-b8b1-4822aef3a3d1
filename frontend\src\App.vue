<script setup lang="ts">
import { onMounted, watch } from 'vue';
import { RouterView } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useUIStore } from '@/stores/ui';
import { useI18n } from 'vue-i18n';

const authStore = useAuthStore();
const uiStore = useUIStore();
const { locale } = useI18n();

// Initialize stores
onMounted(async () => {
  uiStore.initialize();
  await authStore.initialize();
});

// Watch for language changes
watch(() => uiStore.language, (newLang) => {
  locale.value = newLang;
}, { immediate: true });
</script>

<template>
  <div
    id="app"
    :class="[
      'min-h-screen bg-gray-50 transition-colors duration-200',
      uiStore.currentTheme === 'dark' ? 'dark' : ''
    ]"
    :dir="uiStore.direction"
  >
    <!-- Main Application -->
    <RouterView />
  </div>
</template>
