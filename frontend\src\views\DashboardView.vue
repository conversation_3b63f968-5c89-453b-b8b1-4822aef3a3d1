<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900">
              {{ t('app.name') }}
            </h1>
          </div>
          
          <div class="flex items-center space-x-4">
            <!-- Language Switcher -->
            <div class="flex space-x-2">
              <button
                @click="uiStore.setLanguage('en')"
                :class="[
                  'px-2 py-1 rounded text-sm',
                  uiStore.language === 'en' 
                    ? 'bg-primary-600 text-white' 
                    : 'text-gray-600 hover:text-gray-900'
                ]"
              >
                EN
              </button>
              <button
                @click="uiStore.setLanguage('ar')"
                :class="[
                  'px-2 py-1 rounded text-sm',
                  uiStore.language === 'ar' 
                    ? 'bg-primary-600 text-white' 
                    : 'text-gray-600 hover:text-gray-900'
                ]"
              >
                ع
              </button>
            </div>
            
            <!-- User Menu -->
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-700">
                {{ t('dashboard.welcome', { name: authStore.userFullName }) }}
              </span>
              <button
                @click="handleLogout"
                class="btn-outline text-sm"
              >
                {{ t('auth.logout') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-medium">{{ taskStore.totalTasks }}</span>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">{{ t('dashboard.totalTasks') }}</p>
                <p class="text-2xl font-semibold text-gray-900">{{ taskStore.totalTasks }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-medium">{{ taskStore.completedTasks.length }}</span>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">{{ t('dashboard.completedTasks') }}</p>
                <p class="text-2xl font-semibold text-gray-900">{{ taskStore.completedTasks.length }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-medium">{{ taskStore.pendingTasks.length }}</span>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">{{ t('dashboard.pendingTasks') }}</p>
                <p class="text-2xl font-semibold text-gray-900">{{ taskStore.pendingTasks.length }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-medium">{{ taskStore.overdueTasks.length }}</span>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">{{ t('dashboard.overdueTasks') }}</p>
                <p class="text-2xl font-semibold text-gray-900">{{ taskStore.overdueTasks.length }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">{{ t('dashboard.quickActions') }}</h2>
        <div class="flex space-x-4">
          <button
            @click="showCreateTaskModal = true"
            class="btn-primary"
          >
            {{ t('tasks.createTask') }}
          </button>
          <router-link to="/tasks" class="btn-outline">
            {{ t('navigation.tasks') }}
          </router-link>
        </div>
      </div>

      <!-- Recent Tasks -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900">{{ t('dashboard.recentTasks') }}</h3>
          </div>
          <div class="card-body">
            <div v-if="taskStore.tasks.length === 0" class="text-center py-8">
              <p class="text-gray-500">{{ t('tasks.noTasks') }}</p>
              <button
                @click="showCreateTaskModal = true"
                class="btn-primary mt-4"
              >
                {{ t('tasks.createFirstTask') }}
              </button>
            </div>
            <div v-else class="space-y-3">
              <div
                v-for="task in recentTasks"
                :key="task.id"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div class="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    :checked="task.isCompleted"
                    @change="toggleTask(task.id)"
                    class="form-checkbox"
                  />
                  <div>
                    <p :class="['text-sm font-medium', task.isCompleted ? 'line-through text-gray-500' : 'text-gray-900']">
                      {{ task.title }}
                    </p>
                    <p class="text-xs text-gray-500">
                      {{ formatDate(task.createdAt) }}
                    </p>
                  </div>
                </div>
                <span :class="['badge', getPriorityClass(task.priority)]">
                  {{ t(`priority.${task.priority.toLowerCase()}`) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900">{{ t('dashboard.upcomingTasks') }}</h3>
          </div>
          <div class="card-body">
            <div v-if="taskStore.tasksDueSoon.length === 0" class="text-center py-8">
              <p class="text-gray-500">{{ t('tasks.noTasks') }}</p>
            </div>
            <div v-else class="space-y-3">
              <div
                v-for="task in taskStore.tasksDueSoon.slice(0, 5)"
                :key="task.id"
                class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg"
              >
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ task.title }}</p>
                  <p class="text-xs text-gray-500">
                    {{ t('tasks.dueSoon') }}: {{ formatDate(task.dueDate!) }}
                  </p>
                </div>
                <span :class="['badge', getPriorityClass(task.priority)]">
                  {{ t(`priority.${task.priority.toLowerCase()}`) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Create Task Modal (simplified) -->
    <div v-if="showCreateTaskModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-medium text-gray-900 mb-4">{{ t('tasks.createTask') }}</h3>
        <form @submit.prevent="createTask">
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ t('tasks.taskTitle') }}
            </label>
            <input
              v-model="newTask.title"
              type="text"
              required
              class="form-input w-full"
              :placeholder="t('tasks.taskTitle')"
            />
          </div>
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ t('tasks.taskDescription') }}
            </label>
            <textarea
              v-model="newTask.description"
              rows="3"
              class="form-textarea w-full"
              :placeholder="t('tasks.taskDescription')"
            ></textarea>
          </div>
          <div class="flex justify-end space-x-3">
            <button
              type="button"
              @click="showCreateTaskModal = false"
              class="btn-outline"
            >
              {{ t('common.cancel') }}
            </button>
            <button
              type="submit"
              :disabled="taskStore.isLoading"
              class="btn-primary"
            >
              {{ t('common.create') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useAuthStore } from '@/stores/auth';
import { useTaskStore } from '@/stores/tasks';
import { useUIStore } from '@/stores/ui';
import type { TaskPriority } from '@/types';

const router = useRouter();
const { t } = useI18n();
const authStore = useAuthStore();
const taskStore = useTaskStore();
const uiStore = useUIStore();

const showCreateTaskModal = ref(false);
const newTask = reactive({
  title: '',
  description: '',
});

const recentTasks = computed(() => taskStore.tasks.slice(0, 5));

onMounted(async () => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
    return;
  }
  
  await taskStore.fetchTasks();
  await taskStore.fetchStatistics();
});

async function handleLogout() {
  try {
    await authStore.logout();
    uiStore.showSuccess(t('auth.logoutSuccess'));
    router.push('/login');
  } catch (error: any) {
    uiStore.showError('Logout failed', error.message);
  }
}

async function createTask() {
  try {
    await taskStore.createTask({
      title: newTask.title,
      description: newTask.description || undefined,
    });
    
    uiStore.showSuccess(t('tasks.taskCreated'));
    showCreateTaskModal.value = false;
    newTask.title = '';
    newTask.description = '';
  } catch (error: any) {
    uiStore.showError('Failed to create task', error.message);
  }
}

async function toggleTask(taskId: string) {
  try {
    await taskStore.toggleTaskCompletion(taskId);
  } catch (error: any) {
    uiStore.showError('Failed to update task', error.message);
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString(uiStore.language === 'ar' ? 'ar-SA' : 'en-US');
}

function getPriorityClass(priority: TaskPriority) {
  switch (priority) {
    case 'URGENT':
      return 'badge-error';
    case 'HIGH':
      return 'badge-warning';
    case 'MEDIUM':
      return 'badge-primary';
    case 'LOW':
      return 'badge-secondary';
    default:
      return 'badge-secondary';
  }
}
</script>
