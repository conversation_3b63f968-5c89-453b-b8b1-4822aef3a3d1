{"name": "bilingual-todo-app", "version": "1.0.0", "description": "A bilingual (Arabic/English) To-Do Application with Vue.js frontend and Node.js backend", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "format": "prettier --write \"**/*.{js,ts,vue,json,md}\"", "setup": "npm install && npm run setup:backend && npm run setup:frontend", "setup:backend": "cd backend && npm install", "setup:frontend": "cd frontend && npm install"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["vue.js", "node.js", "typescript", "todo", "bilingual", "arabic", "english", "clean-architecture"], "author": "Your Name", "license": "MIT"}