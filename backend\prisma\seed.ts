import { PrismaClient, Priority } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

/**
 * Seeds the database with initial data for development and testing
 */
async function main(): Promise<void> {
  console.log('🌱 Starting database seeding...');

  // Create demo users
  const hashedPassword = await bcrypt.hash('password123', 12);

  const englishUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'john_doe',
      password: hashedPassword,
      firstName: 'John',
      lastName: 'Doe',
      language: 'en',
    },
  });

  const arabicUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'ahmed_ali',
      password: hashedPassword,
      firstName: 'أحمد',
      lastName: 'علي',
      language: 'ar',
    },
  });

  console.log('👥 Created demo users');

  // Create demo tasks for English user
  const englishTasks = [
    {
      title: 'Complete project documentation',
      description: 'Write comprehensive documentation for the todo application',
      priority: Priority.HIGH,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      userId: englishUser.id,
    },
    {
      title: 'Review code changes',
      description: 'Review pull requests from team members',
      priority: Priority.MEDIUM,
      isCompleted: true,
      userId: englishUser.id,
    },
    {
      title: 'Buy groceries',
      description: 'Milk, bread, eggs, and vegetables',
      priority: Priority.LOW,
      dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
      userId: englishUser.id,
    },
  ];

  // Create demo tasks for Arabic user
  const arabicTasks = [
    {
      title: 'إنهاء تقرير المشروع',
      description: 'كتابة تقرير شامل عن حالة المشروع الحالية',
      priority: Priority.URGENT,
      dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
      userId: arabicUser.id,
    },
    {
      title: 'مراجعة الكود',
      description: 'مراجعة التحديثات الجديدة في الكود',
      priority: Priority.HIGH,
      userId: arabicUser.id,
    },
    {
      title: 'شراء مستلزمات المكتب',
      description: 'أقلام، دفاتر، وأوراق طباعة',
      priority: Priority.MEDIUM,
      isCompleted: true,
      userId: arabicUser.id,
    },
    {
      title: 'حجز موعد طبيب',
      description: 'حجز موعد للفحص الدوري',
      priority: Priority.LOW,
      dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
      userId: arabicUser.id,
    },
  ];

  // Insert tasks
  for (const task of [...englishTasks, ...arabicTasks]) {
    await prisma.task.create({
      data: task,
    });
  }

  console.log('📝 Created demo tasks');
  console.log('✅ Database seeding completed successfully!');
}

main()
  .catch(e => {
    console.error('❌ Error during database seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
