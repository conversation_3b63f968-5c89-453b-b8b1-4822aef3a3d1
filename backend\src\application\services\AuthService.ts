import { User } from '../../domain/entities/User';
import { IUserRepository } from '../../domain/interfaces/IUserRepository';
import { AuthUtils, JwtPayload, TokenPair } from '../../shared/utils/auth';
import { logger } from '../../shared/utils/logger';

/**
 * Login request data
 */
export interface LoginRequest {
  email: string;
  password: string;
}

/**
 * Registration request data
 */
export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
  language?: 'en' | 'ar';
}

/**
 * Authentication response data
 */
export interface AuthResponse {
  user: Omit<User, 'password'>;
  tokens: TokenPair;
}

/**
 * Authentication service handling user authentication and authorization
 */
export class AuthService {
  constructor(private readonly userRepository: IUserRepository) {}

  /**
   * Registers a new user
   */
  async register(request: RegisterRequest): Promise<AuthResponse> {
    try {
      // Check if email is already taken
      const existingUserByEmail = await this.userRepository.findByEmail(request.email);
      if (existingUserByEmail) {
        throw new Error('Email address is already registered');
      }

      // Check if username is already taken
      const existingUserByUsername = await this.userRepository.findByUsername(request.username);
      if (existingUserByUsername) {
        throw new Error('Username is already taken');
      }

      // Hash the password
      const hashedPassword = await AuthUtils.hashPassword(request.password);

      // Create the user
      const user = await this.userRepository.create({
        email: request.email,
        username: request.username,
        password: hashedPassword,
        firstName: request.firstName,
        lastName: request.lastName,
        language: request.language || 'en',
      });

      // Generate tokens
      const jwtPayload: JwtPayload = {
        userId: user.id,
        email: user.email,
        username: user.username,
        language: user.language,
      };
      const tokens = AuthUtils.generateTokenPair(jwtPayload);

      logger.info('User registered successfully', { userId: user.id, email: user.email });

      return {
        user: user.toSafeObject(),
        tokens,
      };
    } catch (error) {
      logger.error('Registration failed', { error, email: request.email });
      throw error;
    }
  }

  /**
   * Authenticates a user with email and password
   */
  async login(request: LoginRequest): Promise<AuthResponse> {
    try {
      // Find user by email
      const user = await this.userRepository.findByEmail(request.email);
      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Check if user is active
      if (!user.isUserActive()) {
        throw new Error('Account is deactivated');
      }

      // Verify password
      const isPasswordValid = await AuthUtils.comparePassword(request.password, user.password);
      if (!isPasswordValid) {
        throw new Error('Invalid email or password');
      }

      // Generate tokens
      const jwtPayload: JwtPayload = {
        userId: user.id,
        email: user.email,
        username: user.username,
        language: user.language,
      };
      const tokens = AuthUtils.generateTokenPair(jwtPayload);

      logger.info('User logged in successfully', { userId: user.id, email: user.email });

      return {
        user: user.toSafeObject(),
        tokens,
      };
    } catch (error) {
      logger.error('Login failed', { error, email: request.email });
      throw error;
    }
  }

  /**
   * Refreshes access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<TokenPair> {
    try {
      // Verify refresh token
      const payload = AuthUtils.verifyRefreshToken(refreshToken);

      // Find user to ensure they still exist and are active
      const user = await this.userRepository.findById(payload.userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (!user.isUserActive()) {
        throw new Error('Account is deactivated');
      }

      // Generate new token pair
      const jwtPayload: JwtPayload = {
        userId: user.id,
        email: user.email,
        username: user.username,
        language: user.language,
      };
      const tokens = AuthUtils.generateTokenPair(jwtPayload);

      logger.info('Token refreshed successfully', { userId: user.id });

      return tokens;
    } catch (error) {
      logger.error('Token refresh failed', { error });
      throw error;
    }
  }

  /**
   * Validates an access token and returns user information
   */
  async validateToken(accessToken: string): Promise<User> {
    try {
      // Verify access token
      const payload = AuthUtils.verifyAccessToken(accessToken);

      // Find user to ensure they still exist and are active
      const user = await this.userRepository.findById(payload.userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (!user.isUserActive()) {
        throw new Error('Account is deactivated');
      }

      return user;
    } catch (error) {
      logger.error('Token validation failed', { error });
      throw error;
    }
  }

  /**
   * Changes user password
   */
  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    try {
      // Find user
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await AuthUtils.comparePassword(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password
      const hashedNewPassword = await AuthUtils.hashPassword(newPassword);

      // Update user password
      await this.userRepository.update(userId, { password: hashedNewPassword });

      logger.info('Password changed successfully', { userId });
    } catch (error) {
      logger.error('Password change failed', { error, userId });
      throw error;
    }
  }
}
