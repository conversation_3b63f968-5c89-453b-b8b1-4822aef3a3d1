#!/bin/bash

# Bilingual Todo App Setup Script
# This script sets up the complete development environment

set -e

echo "🚀 Setting up Bilingual Todo Application..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ and try again."
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node -v)"
        exit 1
    fi
    
    print_success "Node.js $(node -v) is installed"
}

# Check if PostgreSQL is installed
check_postgres() {
    if ! command -v psql &> /dev/null; then
        print_warning "PostgreSQL is not installed or not in PATH."
        print_warning "Please install PostgreSQL 13+ and ensure it's running."
        print_warning "You can continue setup and configure the database later."
    else
        print_success "PostgreSQL is available"
    fi
}

# Install root dependencies
install_root_deps() {
    print_status "Installing root dependencies..."
    npm install
    print_success "Root dependencies installed"
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd backend
    
    # Install dependencies
    print_status "Installing backend dependencies..."
    npm install
    
    # Copy environment file
    if [ ! -f .env ]; then
        print_status "Creating backend environment file..."
        cp .env.example .env
        print_warning "Please update backend/.env with your database credentials"
    fi
    
    # Generate Prisma client
    print_status "Generating Prisma client..."
    npx prisma generate
    
    cd ..
    print_success "Backend setup completed"
}

# Setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    
    cd frontend
    
    # Install additional dependencies
    print_status "Installing frontend dependencies..."
    npm install axios vue-i18n@9 @tailwindcss/forms tailwindcss postcss autoprefixer @headlessui/vue @heroicons/vue
    
    # Initialize Tailwind CSS
    print_status "Initializing Tailwind CSS..."
    npx tailwindcss init -p
    
    # Copy environment file
    if [ ! -f .env ]; then
        print_status "Creating frontend environment file..."
        cp .env.example .env
    fi
    
    cd ..
    print_success "Frontend setup completed"
}

# Create database and run migrations
setup_database() {
    print_status "Setting up database..."
    
    cd backend
    
    # Check if DATABASE_URL is set
    if grep -q "postgresql://username:password@localhost:5432/todo_db" .env; then
        print_warning "Please update DATABASE_URL in backend/.env with your actual database credentials"
        print_warning "Skipping database migration for now"
    else
        print_status "Running database migrations..."
        if npx prisma migrate dev --name init; then
            print_success "Database migrations completed"
            
            print_status "Seeding database with sample data..."
            if npx prisma db seed; then
                print_success "Database seeded successfully"
            else
                print_warning "Database seeding failed. You can run 'npm run db:seed' later"
            fi
        else
            print_warning "Database migration failed. Please check your database connection"
        fi
    fi
    
    cd ..
}

# Create logs directory
create_logs_dir() {
    print_status "Creating logs directory..."
    mkdir -p backend/logs
    print_success "Logs directory created"
}

# Main setup function
main() {
    echo "=============================================="
    echo "🌟 Bilingual Todo App Setup"
    echo "=============================================="
    
    check_node
    check_postgres
    
    install_root_deps
    setup_backend
    setup_frontend
    create_logs_dir
    setup_database
    
    echo ""
    echo "=============================================="
    print_success "Setup completed successfully! 🎉"
    echo "=============================================="
    echo ""
    echo "📋 Next steps:"
    echo ""
    echo "1. Update database configuration:"
    echo "   - Edit backend/.env with your PostgreSQL credentials"
    echo "   - Run: cd backend && npm run db:migrate"
    echo ""
    echo "2. Start the development servers:"
    echo "   - Backend: cd backend && npm run dev"
    echo "   - Frontend: cd frontend && npm run dev"
    echo "   - Or both: npm run dev (from root)"
    echo ""
    echo "3. Access the application:"
    echo "   - Frontend: http://localhost:5173"
    echo "   - Backend API: http://localhost:3000"
    echo "   - API Docs: http://localhost:3000/api-docs"
    echo ""
    echo "4. Default test accounts (after seeding):"
    echo "   - English: <EMAIL> / password123"
    echo "   - Arabic: <EMAIL> / password123"
    echo ""
    echo "🔧 Useful commands:"
    echo "   - npm run dev          # Start both servers"
    echo "   - npm run build        # Build for production"
    echo "   - npm run test         # Run all tests"
    echo "   - npm run lint         # Lint all code"
    echo ""
    print_success "Happy coding! 🚀"
}

# Run main function
main
