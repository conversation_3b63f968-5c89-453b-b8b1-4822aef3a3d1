import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../../application/services/AuthService';
import { AuthUtils } from '../../shared/utils/auth';
import { logger } from '../../shared/utils/logger';

/**
 * Extended Request interface with user information
 */
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    username: string;
    language: 'en' | 'ar';
  };
}

/**
 * Authentication middleware factory
 */
export const createAuthMiddleware = (authService: AuthService) => {
  /**
   * Middleware to authenticate requests using JWT tokens
   */
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      const token = AuthUtils.extractTokenFromHeader(authHeader);

      if (!token) {
        res.status(401).json({
          success: false,
          message: 'Access token is required',
          code: 'MISSING_TOKEN',
        });
        return;
      }

      // Validate token and get user
      const user = await authService.validateToken(token);

      // Add user information to request
      req.user = {
        id: user.id,
        email: user.email,
        username: user.username,
        language: user.language,
      };

      next();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('Authentication failed', { error: errorMessage });

      let statusCode = 401;
      let code = 'INVALID_TOKEN';
      let message = 'Invalid or expired access token';

      if (errorMessage.includes('expired')) {
        code = 'TOKEN_EXPIRED';
        message = 'Access token has expired';
      } else if (errorMessage.includes('deactivated')) {
        statusCode = 403;
        code = 'ACCOUNT_DEACTIVATED';
        message = 'Account is deactivated';
      } else if (errorMessage.includes('not found')) {
        code = 'USER_NOT_FOUND';
        message = 'User not found';
      }

      res.status(statusCode).json({
        success: false,
        message,
        code,
      });
    }
  };
};

/**
 * Optional authentication middleware
 * Adds user information to request if token is provided, but doesn't require it
 */
export const createOptionalAuthMiddleware = (authService: AuthService) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      const token = AuthUtils.extractTokenFromHeader(authHeader);

      if (token) {
        try {
          const user = await authService.validateToken(token);
          req.user = {
            id: user.id,
            email: user.email,
            username: user.username,
            language: user.language,
          };
        } catch (error) {
          // Ignore authentication errors for optional auth
          const errorMessage = error instanceof Error ? error.message : String(error);
          logger.debug('Optional authentication failed', { error: errorMessage });
        }
      }

      next();
    } catch (error) {
      // Continue without authentication for optional middleware
      next();
    }
  };
};
