// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String?  @map("first_name")
  lastName  String?  @map("last_name")
  language  String   @default("en") // 'en' or 'ar'
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  tasks         Task[]
  refreshTokens RefreshToken[]

  @@map("users")
}

model Task {
  id          String    @id @default(cuid())
  title       String
  description String?
  isCompleted Boolean   @default(false) @map("is_completed")
  priority    String    @default("MEDIUM")
  dueDate     DateTime? @map("due_date")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Foreign Keys
  userId String @map("user_id")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("tasks")
  @@index([userId])
  @@index([isCompleted])
  @@index([priority])
  @@index([dueDate])
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")

  // Foreign Keys
  userId String @map("user_id")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
  @@index([userId])
  @@index([expiresAt])
}


