import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { authService } from '@/services/auth';
import type { User, LoginRequest, CreateUserRequest, AuthResponse } from '@/types';

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // Getters
  const isAuthenticated = computed(() => !!user.value);
  const userFullName = computed(() => {
    if (!user.value) return '';
    const { firstName, lastName, username } = user.value;
    if (firstName || lastName) {
      return [firstName, lastName].filter(Boolean).join(' ');
    }
    return username;
  });

  // Actions
  async function login(credentials: LoginRequest): Promise<void> {
    isLoading.value = true;
    error.value = null;
    
    try {
      const response: AuthResponse = await authService.login(credentials);
      user.value = response.user;
    } catch (err: any) {
      error.value = err.message || 'Login failed';
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  async function register(userData: CreateUserRequest): Promise<void> {
    isLoading.value = true;
    error.value = null;
    
    try {
      const response: AuthResponse = await authService.register(userData);
      user.value = response.user;
    } catch (err: any) {
      error.value = err.message || 'Registration failed';
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  async function logout(): Promise<void> {
    isLoading.value = true;
    
    try {
      await authService.logout();
    } catch (err: any) {
      console.warn('Logout error:', err);
    } finally {
      user.value = null;
      error.value = null;
      isLoading.value = false;
    }
  }

  async function fetchProfile(): Promise<void> {
    if (!authService.isAuthenticated()) return;
    
    isLoading.value = true;
    error.value = null;
    
    try {
      user.value = await authService.getProfile();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch profile';
      if (err.response?.status === 401) {
        user.value = null;
        authService.logout();
      }
    } finally {
      isLoading.value = false;
    }
  }

  async function changePassword(currentPassword: string, newPassword: string): Promise<void> {
    isLoading.value = true;
    error.value = null;
    
    try {
      await authService.changePassword(currentPassword, newPassword);
    } catch (err: any) {
      error.value = err.message || 'Failed to change password';
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  function clearError(): void {
    error.value = null;
  }

  async function initialize(): Promise<void> {
    if (authService.isAuthenticated()) {
      await fetchProfile();
    }
  }

  return {
    user,
    isLoading,
    error,
    isAuthenticated,
    userFullName,
    login,
    register,
    logout,
    fetchProfile,
    changePassword,
    clearError,
    initialize,
  };
});
