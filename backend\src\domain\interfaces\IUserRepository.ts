import { User } from '../entities/User';

/**
 * Data for creating a new user
 */
export interface CreateUserData {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
  language?: 'en' | 'ar';
}

/**
 * Data for updating an existing user
 */
export interface UpdateUserData {
  email?: string;
  username?: string;
  password?: string;
  firstName?: string;
  lastName?: string;
  language?: 'en' | 'ar';
  isActive?: boolean;
}

/**
 * Repository interface for user data access operations
 * Implements the Repository pattern for clean separation of concerns
 */
export interface IUserRepository {
  /**
   * Creates a new user
   */
  create(data: CreateUserData): Promise<User>;

  /**
   * Finds a user by their ID
   */
  findById(id: string): Promise<User | null>;

  /**
   * Finds a user by their email address
   */
  findByEmail(email: string): Promise<User | null>;

  /**
   * Finds a user by their username
   */
  findByUsername(username: string): Promise<User | null>;

  /**
   * Updates an existing user
   */
  update(id: string, data: UpdateUserData): Promise<User | null>;

  /**
   * Deletes a user by their ID
   */
  delete(id: string): Promise<boolean>;

  /**
   * Checks if an email address is already taken
   */
  isEmailTaken(email: string, excludeUserId?: string): Promise<boolean>;

  /**
   * Checks if a username is already taken
   */
  isUsernameTaken(username: string, excludeUserId?: string): Promise<boolean>;

  /**
   * Finds all active users (for admin purposes)
   */
  findAllActive(): Promise<User[]>;

  /**
   * Deactivates a user account
   */
  deactivate(id: string): Promise<boolean>;

  /**
   * Activates a user account
   */
  activate(id: string): Promise<boolean>;
}
