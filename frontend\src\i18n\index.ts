import { createI18n } from 'vue-i18n';
import en from '@/locales/en.json';
import ar from '@/locales/ar.json';

// Get default language from localStorage or browser
function getDefaultLanguage(): string {
  const saved = localStorage.getItem('language');
  if (saved && ['en', 'ar'].includes(saved)) {
    return saved;
  }
  
  // Detect browser language
  const browserLang = navigator.language.split('-')[0];
  return browserLang === 'ar' ? 'ar' : 'en';
}

// Create i18n instance
export const i18n = createI18n({
  legacy: false,
  locale: getDefaultLanguage(),
  fallbackLocale: 'en',
  messages: {
    en,
    ar,
  },
  globalInjection: true,
  warnHtmlMessage: false,
});

// Helper function to change language
export function setLanguage(locale: string): void {
  if (i18n.global.availableLocales.includes(locale)) {
    i18n.global.locale.value = locale;
    localStorage.setItem('language', locale);
    
    // Update document attributes
    document.documentElement.setAttribute('lang', locale);
    document.documentElement.setAttribute('dir', locale === 'ar' ? 'rtl' : 'ltr');
  }
}

// Initialize document attributes
const currentLocale = i18n.global.locale.value;
document.documentElement.setAttribute('lang', currentLocale);
document.documentElement.setAttribute('dir', currentLocale === 'ar' ? 'rtl' : 'ltr');

export default i18n;
