import { PrismaClient as PrismaClientType } from '@prisma/client';
import { logger } from '../../shared/utils/logger';

/**
 * Singleton Prisma client instance
 * Manages database connections and provides query logging
 */
class PrismaClientSingleton {
  private static instance: PrismaClientType | null = null;

  /**
   * Gets the singleton Prisma client instance
   */
  public static getInstance(): PrismaClientType {
    if (!PrismaClientSingleton.instance) {
      PrismaClientSingleton.instance = new PrismaClientType({
        log: [
          {
            emit: 'event',
            level: 'query',
          },
          {
            emit: 'event',
            level: 'error',
          },
          {
            emit: 'event',
            level: 'info',
          },
          {
            emit: 'event',
            level: 'warn',
          },
        ],
      });

      // Set up logging event handlers
      PrismaClientSingleton.instance.$on('query', e => {
        logger.debug('Prisma Query', {
          query: e.query,
          params: e.params,
          duration: `${e.duration}ms`,
        });
      });

      PrismaClientSingleton.instance.$on('error', e => {
        logger.error('Prisma Error', {
          message: e.message,
          target: e.target,
        });
      });

      PrismaClientSingleton.instance.$on('info', e => {
        logger.info('Prisma Info', {
          message: e.message,
          target: e.target,
        });
      });

      PrismaClientSingleton.instance.$on('warn', e => {
        logger.warn('Prisma Warning', {
          message: e.message,
          target: e.target,
        });
      });
    }

    return PrismaClientSingleton.instance;
  }

  /**
   * Disconnects the Prisma client
   */
  public static async disconnect(): Promise<void> {
    if (PrismaClientSingleton.instance) {
      await PrismaClientSingleton.instance.$disconnect();
      PrismaClientSingleton.instance = null;
    }
  }

  /**
   * Connects to the database
   */
  public static async connect(): Promise<void> {
    const client = PrismaClientSingleton.getInstance();
    await client.$connect();
    logger.info('Connected to database');
  }
}

export const prisma = PrismaClientSingleton.getInstance();
export { PrismaClientSingleton };
