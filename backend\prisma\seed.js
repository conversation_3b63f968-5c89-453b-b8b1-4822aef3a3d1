"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Starting database seeding...');
    const hashedPassword = await bcryptjs_1.default.hash('password123', 12);
    const englishUser = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            username: 'john_doe',
            password: hashedPassword,
            firstName: '<PERSON>',
            lastName: 'Doe',
            language: 'en',
        },
    });
    const arabicUser = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            username: 'ahmed_ali',
            password: hashedPassword,
            firstName: 'أحمد',
            lastName: 'علي',
            language: 'ar',
        },
    });
    console.log('👥 Created demo users');
    const englishTasks = [
        {
            title: 'Complete project documentation',
            description: 'Write comprehensive documentation for the todo application',
            priority: client_1.Priority.HIGH,
            dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            userId: englishUser.id,
        },
        {
            title: 'Review code changes',
            description: 'Review pull requests from team members',
            priority: client_1.Priority.MEDIUM,
            isCompleted: true,
            userId: englishUser.id,
        },
        {
            title: 'Buy groceries',
            description: 'Milk, bread, eggs, and vegetables',
            priority: client_1.Priority.LOW,
            dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
            userId: englishUser.id,
        },
    ];
    const arabicTasks = [
        {
            title: 'إنهاء تقرير المشروع',
            description: 'كتابة تقرير شامل عن حالة المشروع الحالية',
            priority: client_1.Priority.URGENT,
            dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
            userId: arabicUser.id,
        },
        {
            title: 'مراجعة الكود',
            description: 'مراجعة التحديثات الجديدة في الكود',
            priority: client_1.Priority.HIGH,
            userId: arabicUser.id,
        },
        {
            title: 'شراء مستلزمات المكتب',
            description: 'أقلام، دفاتر، وأوراق طباعة',
            priority: client_1.Priority.MEDIUM,
            isCompleted: true,
            userId: arabicUser.id,
        },
        {
            title: 'حجز موعد طبيب',
            description: 'حجز موعد للفحص الدوري',
            priority: client_1.Priority.LOW,
            dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
            userId: arabicUser.id,
        },
    ];
    for (const task of [...englishTasks, ...arabicTasks]) {
        await prisma.task.create({
            data: task,
        });
    }
    console.log('📝 Created demo tasks');
    console.log('✅ Database seeding completed successfully!');
}
main()
    .catch(e => {
    console.error('❌ Error during database seeding:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map