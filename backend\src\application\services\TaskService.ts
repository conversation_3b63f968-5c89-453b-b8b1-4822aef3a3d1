import { Task, TaskPriority } from '../../domain/entities/Task';
import {
  ITaskRepository,
  TaskFilterOptions,
  CreateTaskData,
  UpdateTaskData,
} from '../../domain/interfaces/ITaskRepository';
import { IUnitOfWork } from '../../domain/interfaces/IUnitOfWork';
import { logger } from '../../shared/utils/logger';

/**
 * Task creation request data
 */
export interface CreateTaskRequest {
  title: string;
  description?: string;
  priority?: TaskPriority;
  dueDate?: Date;
}

/**
 * Task update request data
 */
export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  isCompleted?: boolean;
  priority?: TaskPriority;
  dueDate?: Date;
}

/**
 * Task query request data
 */
export interface TaskQueryRequest extends TaskFilterOptions {}

/**
 * Paginated task response
 */
export interface PaginatedTaskResponse {
  tasks: Task[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Task statistics
 */
export interface TaskStatistics {
  total: number;
  completed: number;
  pending: number;
  overdue: number;
  dueSoon: number;
  byPriority: {
    low: number;
    medium: number;
    high: number;
    urgent: number;
  };
}

/**
 * Task service handling business logic for task operations
 */
export class TaskService {
  constructor(
    private readonly taskRepository: ITaskRepository,
    private readonly unitOfWork: IUnitOfWork
  ) {}

  /**
   * Creates a new task for a user
   */
  async createTask(userId: string, request: CreateTaskRequest): Promise<Task> {
    try {
      const taskData: CreateTaskData = {
        title: request.title.trim(),
        description: request.description?.trim(),
        priority: request.priority || TaskPriority.MEDIUM,
        dueDate: request.dueDate,
        userId,
      };

      const task = await this.taskRepository.create(taskData);
      logger.info('Task created', { taskId: task.id, userId });
      return task;
    } catch (error) {
      logger.error('Failed to create task', { error, userId, request });
      throw error;
    }
  }

  /**
   * Gets a task by ID, ensuring it belongs to the user
   */
  async getTaskById(taskId: string, userId: string): Promise<Task> {
    try {
      const task = await this.taskRepository.findById(taskId);
      if (!task) {
        throw new Error('Task not found');
      }

      if (task.userId !== userId) {
        throw new Error('Access denied: Task does not belong to user');
      }

      return task;
    } catch (error) {
      logger.error('Failed to get task', { error, taskId, userId });
      throw error;
    }
  }

  /**
   * Gets all tasks for a user with filtering and pagination
   */
  async getUserTasks(userId: string, query: TaskQueryRequest): Promise<PaginatedTaskResponse> {
    try {
      const limit = query.limit || 20;
      const offset = query.offset || 0;
      const page = Math.floor(offset / limit) + 1;

      const [tasks, total] = await Promise.all([
        this.taskRepository.findByUserId(userId, query),
        this.taskRepository.countByUserId(userId, {
          isCompleted: query.isCompleted,
          priority: query.priority,
          search: query.search,
          dueDateFrom: query.dueDateFrom,
          dueDateTo: query.dueDateTo,
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        tasks,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      logger.error('Failed to get user tasks', { error, userId, query });
      throw error;
    }
  }

  /**
   * Updates a task
   */
  async updateTask(taskId: string, userId: string, request: UpdateTaskRequest): Promise<Task> {
    try {
      // Verify task exists and belongs to user
      await this.getTaskById(taskId, userId);

      const updateData: UpdateTaskData = {
        ...(request.title !== undefined && { title: request.title.trim() }),
        ...(request.description !== undefined && { description: request.description?.trim() }),
        ...(request.isCompleted !== undefined && { isCompleted: request.isCompleted }),
        ...(request.priority !== undefined && { priority: request.priority }),
        ...(request.dueDate !== undefined && { dueDate: request.dueDate }),
      };

      const updatedTask = await this.taskRepository.update(taskId, updateData);
      if (!updatedTask) {
        throw new Error('Failed to update task');
      }

      logger.info('Task updated', { taskId, userId });
      return updatedTask;
    } catch (error) {
      logger.error('Failed to update task', { error, taskId, userId, request });
      throw error;
    }
  }

  /**
   * Deletes a task
   */
  async deleteTask(taskId: string, userId: string): Promise<void> {
    try {
      // Verify task exists and belongs to user
      await this.getTaskById(taskId, userId);

      const deleted = await this.taskRepository.delete(taskId);
      if (!deleted) {
        throw new Error('Failed to delete task');
      }

      logger.info('Task deleted', { taskId, userId });
    } catch (error) {
      logger.error('Failed to delete task', { error, taskId, userId });
      throw error;
    }
  }

  /**
   * Toggles task completion status
   */
  async toggleTaskCompletion(taskId: string, userId: string): Promise<Task> {
    try {
      const task = await this.getTaskById(taskId, userId);
      const updatedTask = await this.updateTask(taskId, userId, {
        isCompleted: !task.isCompleted,
      });

      logger.info('Task completion toggled', { taskId, userId, isCompleted: updatedTask.isCompleted });
      return updatedTask;
    } catch (error) {
      logger.error('Failed to toggle task completion', { error, taskId, userId });
      throw error;
    }
  }

  /**
   * Gets task statistics for a user
   */
  async getTaskStatistics(userId: string): Promise<TaskStatistics> {
    try {
      const [
        allTasks,
        completedTasks,
        overdueTasks,
        dueSoonTasks,
      ] = await Promise.all([
        this.taskRepository.findByUserId(userId),
        this.taskRepository.findByUserId(userId, { isCompleted: true }),
        this.taskRepository.findOverdueByUserId(userId),
        this.taskRepository.findDueSoonByUserId(userId, 7), // Due within 7 days
      ]);

      const byPriority = {
        low: allTasks.filter(task => task.priority === TaskPriority.LOW).length,
        medium: allTasks.filter(task => task.priority === TaskPriority.MEDIUM).length,
        high: allTasks.filter(task => task.priority === TaskPriority.HIGH).length,
        urgent: allTasks.filter(task => task.priority === TaskPriority.URGENT).length,
      };

      return {
        total: allTasks.length,
        completed: completedTasks.length,
        pending: allTasks.length - completedTasks.length,
        overdue: overdueTasks.length,
        dueSoon: dueSoonTasks.length,
        byPriority,
      };
    } catch (error) {
      logger.error('Failed to get task statistics', { error, userId });
      throw error;
    }
  }

  /**
   * Bulk updates multiple tasks
   */
  async bulkUpdateTasks(
    taskIds: string[],
    userId: string,
    updateData: Pick<UpdateTaskRequest, 'isCompleted' | 'priority'>
  ): Promise<Task[]> {
    try {
      return await this.unitOfWork.executeInTransaction(async () => {
        // Verify all tasks belong to the user
        for (const taskId of taskIds) {
          await this.getTaskById(taskId, userId);
        }

        const updatedTasks = await this.taskRepository.bulkUpdate(taskIds, updateData);
        logger.info('Tasks bulk updated', { count: updatedTasks.length, userId });
        return updatedTasks;
      });
    } catch (error) {
      logger.error('Failed to bulk update tasks', { error, taskIds, userId, updateData });
      throw error;
    }
  }

  /**
   * Bulk deletes multiple tasks
   */
  async bulkDeleteTasks(taskIds: string[], userId: string): Promise<number> {
    try {
      return await this.unitOfWork.executeInTransaction(async () => {
        // Verify all tasks belong to the user
        for (const taskId of taskIds) {
          await this.getTaskById(taskId, userId);
        }

        const deletedCount = await this.taskRepository.bulkDelete(taskIds);
        logger.info('Tasks bulk deleted', { count: deletedCount, userId });
        return deletedCount;
      });
    } catch (error) {
      logger.error('Failed to bulk delete tasks', { error, taskIds, userId });
      throw error;
    }
  }
}
