import { Request, Response } from 'express';
import { AuthService } from '../../application/services/AuthService';
import { logger } from '../../shared/utils/logger';
import { asyncHandler } from '../middleware/errorHandler';

/**
 * Authentication controller handling auth-related HTTP requests
 */
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  /**
   * Register a new user
   * POST /api/auth/register
   */
  register = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { email, username, password, firstName, lastName, language } = req.body;

    const result = await this.authService.register({
      email,
      username,
      password,
      firstName,
      lastName,
      language,
    });

    logger.info('User registration successful', { 
      userId: result.user.id, 
      email: result.user.email 
    });

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: result.user,
        tokens: result.tokens,
      },
    });
  });

  /**
   * Login user
   * POST /api/auth/login
   */
  login = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { email, password } = req.body;

    const result = await this.authService.login({ email, password });

    logger.info('User login successful', { 
      userId: result.user.id, 
      email: result.user.email 
    });

    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        user: result.user,
        tokens: result.tokens,
      },
    });
  });

  /**
   * Refresh access token
   * POST /api/auth/refresh
   */
  refreshToken = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      res.status(400).json({
        success: false,
        message: 'Refresh token is required',
        code: 'MISSING_REFRESH_TOKEN',
      });
      return;
    }

    const tokens = await this.authService.refreshToken(refreshToken);

    res.status(200).json({
      success: true,
      message: 'Token refreshed successfully',
      data: { tokens },
    });
  });

  /**
   * Change user password
   * POST /api/auth/change-password
   */
  changePassword = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { currentPassword, newPassword } = req.body;
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTHENTICATION_REQUIRED',
      });
      return;
    }

    await this.authService.changePassword(userId, currentPassword, newPassword);

    logger.info('Password changed successfully', { userId });

    res.status(200).json({
      success: true,
      message: 'Password changed successfully',
    });
  });

  /**
   * Get current user profile
   * GET /api/auth/profile
   */
  getProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = (req as any).user;

    if (!user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTHENTICATION_REQUIRED',
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Profile retrieved successfully',
      data: { user },
    });
  });

  /**
   * Logout user (client-side token removal)
   * POST /api/auth/logout
   */
  logout = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const userId = (req as any).user?.id;

    if (userId) {
      logger.info('User logout', { userId });
    }

    res.status(200).json({
      success: true,
      message: 'Logout successful',
    });
  });
}
