import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { logger } from '../../shared/utils/logger';

/**
 * Validation target types
 */
type ValidationTarget = 'body' | 'query' | 'params';

/**
 * Validation middleware factory
 */
export const validate = (schema: Joi.ObjectSchema, target: ValidationTarget = 'body') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const dataToValidate = req[target];
      
      const { error, value } = schema.validate(dataToValidate, {
        abortEarly: false,
        stripUnknown: true,
        allowUnknown: false,
      });

      if (error) {
        const errorDetails = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value,
        }));

        logger.warn('Validation failed', {
          target,
          errors: errorDetails,
          originalData: dataToValidate,
        });

        res.status(400).json({
          success: false,
          message: 'Validation failed',
          code: 'VALIDATION_ERROR',
          errors: errorDetails,
        });
        return;
      }

      // Replace the original data with validated and sanitized data
      req[target] = value;
      next();
    } catch (error) {
      logger.error('Validation middleware error', { error, target });
      res.status(500).json({
        success: false,
        message: 'Internal validation error',
        code: 'VALIDATION_INTERNAL_ERROR',
      });
    }
  };
};

/**
 * Middleware to validate request body
 */
export const validateBody = (schema: Joi.ObjectSchema) => validate(schema, 'body');

/**
 * Middleware to validate query parameters
 */
export const validateQuery = (schema: Joi.ObjectSchema) => validate(schema, 'query');

/**
 * Middleware to validate route parameters
 */
export const validateParams = (schema: Joi.ObjectSchema) => validate(schema, 'params');
