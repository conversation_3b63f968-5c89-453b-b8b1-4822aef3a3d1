/**
 * User domain entity representing a user in the system
 */
export class User {
  constructor(
    public readonly id: string,
    public readonly email: string,
    public readonly username: string,
    public readonly password: string,
    public readonly firstName: string | null,
    public readonly lastName: string | null,
    public readonly language: 'en' | 'ar',
    public readonly isActive: boolean,
    public readonly createdAt: Date,
    public readonly updatedAt: Date
  ) {}

  /**
   * Gets the user's full name
   */
  public getFullName(): string {
    if (!this.firstName && !this.lastName) {
      return this.username;
    }
    return [this.firstName, this.lastName].filter(Boolean).join(' ');
  }

  /**
   * Checks if the user is active
   */
  public isUserActive(): boolean {
    return this.isActive;
  }

  /**
   * Gets the user's preferred language
   */
  public getLanguage(): 'en' | 'ar' {
    return this.language;
  }

  /**
   * Checks if the user prefers Arabic language
   */
  public isArabicUser(): boolean {
    return this.language === 'ar';
  }

  /**
   * Creates a new User instance without sensitive data
   */
  public toSafeObject(): Omit<User, 'password'> {
    const { password, ...safeUser } = this;
    return safeUser;
  }

  /**
   * Creates a User instance from database data
   */
  public static fromDatabase(data: {
    id: string;
    email: string;
    username: string;
    password: string;
    firstName: string | null;
    lastName: string | null;
    language: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
  }): User {
    return new User(
      data.id,
      data.email,
      data.username,
      data.password,
      data.firstName,
      data.lastName,
      data.language as 'en' | 'ar',
      data.isActive,
      data.createdAt,
      data.updatedAt
    );
  }
}
