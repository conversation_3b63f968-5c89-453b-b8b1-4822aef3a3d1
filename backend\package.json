{"name": "todo-backend", "version": "1.0.0", "description": "Backend API for bilingual todo application", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio", "docs": "swagger-jsdoc -d swaggerDef.js src/**/*.ts -o swagger.json"}, "dependencies": {"@prisma/client": "^5.1.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.1", "prisma": "^5.1.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.10.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.4.5", "@types/supertest": "^2.0.12", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "eslint": "^8.45.0", "jest": "^29.6.1", "nodemon": "^3.0.1", "prettier": "^3.0.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0"}, "keywords": ["nodejs", "express", "typescript", "prisma", "clean-architecture", "repository-pattern", "unit-of-work"], "prisma": {"seed": "ts-node prisma/seed.ts"}}