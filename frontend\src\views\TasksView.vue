<template>
  <div class="tasks-container p-4">
    <h1 class="text-2xl font-bold mb-6">{{ $t('tasks.title') }}</h1>
    
    <div class="mb-6 flex justify-between items-center">
      <div class="flex space-x-2">
        <button 
          @click="showAddTaskModal = true" 
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center"
        >
          <span>{{ $t('tasks.addTask') }}</span>
        </button>
        
        <div class="relative">
          <input 
            type="text" 
            v-model="searchQuery" 
            :placeholder="$t('tasks.search')" 
            class="border rounded-md px-3 py-2 w-64"
          />
        </div>
      </div>
      
      <div class="flex space-x-2">
        <select 
          v-model="filterStatus" 
          class="border rounded-md px-3 py-2"
        >
          <option value="all">{{ $t('tasks.filterAll') }}</option>
          <option value="completed">{{ $t('tasks.filterCompleted') }}</option>
          <option value="active">{{ $t('tasks.filterActive') }}</option>
        </select>
      </div>
    </div>
    
    <div v-if="loading" class="flex justify-center my-8">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>
    
    <div v-else-if="filteredTasks.length === 0" class="text-center my-8 text-gray-500">
      {{ $t('tasks.noTasks') }}
    </div>
    
    <div v-else class="space-y-4">
      <!-- Task list will go here -->
      <div 
        v-for="task in filteredTasks" 
        :key="task.id" 
        class="border rounded-lg p-4 hover:shadow-md transition-shadow"
      >
        <div class="flex items-start justify-between">
          <div class="flex items-start space-x-3">
            <input 
              type="checkbox" 
              :checked="task.completed" 
              @change="toggleTask(task.id)"
              class="mt-1"
            />
            <div>
              <h3 
                class="text-lg font-medium" 
                :class="{ 'line-through text-gray-500': task.completed }"
              >
                {{ task.title }}
              </h3>
              <p class="text-gray-600 mt-1" v-if="task.description">{{ task.description }}</p>
              <div class="text-sm text-gray-500 mt-2">
                {{ $t('tasks.createdAt') }}: {{ formatDate(task.createdAt) }}
              </div>
            </div>
          </div>
          
          <div class="flex space-x-2">
            <button 
              @click="editTask(task)" 
              class="text-blue-600 hover:text-blue-800"
            >
              {{ $t('common.edit') }}
            </button>
            <button 
              @click="deleteTask(task.id)" 
              class="text-red-600 hover:text-red-800"
            >
              {{ $t('common.delete') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useTaskStore } from '@/stores/task';
import { useUIStore } from '@/stores/ui';

const { t } = useI18n();
const taskStore = useTaskStore();
const uiStore = useUIStore();

const loading = ref(true);
const showAddTaskModal = ref(false);
const searchQuery = ref('');
const filterStatus = ref('all');

// Computed property for filtered tasks
const filteredTasks = computed(() => {
  let result = taskStore.tasks;
  
  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(task => 
      task.title.toLowerCase().includes(query) || 
      (task.description && task.description.toLowerCase().includes(query))
    );
  }
  
  // Filter by status
  if (filterStatus.value === 'completed') {
    result = result.filter(task => task.completed);
  } else if (filterStatus.value === 'active') {
    result = result.filter(task => !task.completed);
  }
  
  return result;
});

onMounted(async () => {
  try {
    await taskStore.fetchTasks();
  } catch (error: any) {
    uiStore.showError(t('tasks.fetchError'), error.message);
  } finally {
    loading.value = false;
  }
});

// Task actions
async function toggleTask(taskId: string) {
  try {
    await taskStore.toggleTaskCompletion(taskId);
  } catch (error: any) {
    uiStore.showError(t('tasks.updateError'), error.message);
  }
}

function editTask(task: any) {
  // Implement edit task functionality
  console.log('Edit task:', task);
}

async function deleteTask(taskId: string) {
  try {
    await taskStore.deleteTask(taskId);
    uiStore.showSuccess(t('tasks.deleteSuccess'));
  } catch (error: any) {
    uiStore.showError(t('tasks.deleteError'), error.message);
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString(
    uiStore.language === 'ar' ? 'ar-SA' : 'en-US'
  );
}
</script>