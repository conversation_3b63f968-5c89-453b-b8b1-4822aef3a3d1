import { PrismaClient, Prisma } from '@prisma/client';
import { IUnitOfWork } from '../../domain/interfaces/IUnitOfWork';
import { ITaskRepository } from '../../domain/interfaces/ITaskRepository';
import { IUserRepository } from '../../domain/interfaces/IUserRepository';
import { PrismaTaskRepository } from '../repositories/PrismaTaskRepository';
import { PrismaUserRepository } from '../repositories/PrismaUserRepository';
import { logger } from '../../shared/utils/logger';

/**
 * Prisma implementation of the Unit of Work pattern
 * Coordinates multiple repository operations and manages transactions
 */
export class PrismaUnitOfWork implements IUnitOfWork {
  private _tasks: ITaskRepository;
  private _users: IUserRepository;
  private _transaction: Prisma.TransactionClient | null = null;

  constructor(private readonly prisma: PrismaClient) {
    this._tasks = new PrismaTaskRepository();
    this._users = new PrismaUserRepository();
  }

  /**
   * Gets the task repository instance
   */
  get tasks(): ITaskRepository {
    return this._tasks;
  }

  /**
   * Gets the user repository instance
   */
  get users(): IUserRepository {
    return this._users;
  }

  /**
   * Begins a new database transaction
   */
  async beginTransaction(): Promise<void> {
    if (this._transaction) {
      throw new Error('Transaction already in progress');
    }

    try {
      // Note: Prisma doesn't support explicit transaction begin/commit
      // We'll use the $transaction method for atomic operations
      logger.debug('Transaction context prepared');
    } catch (error) {
      logger.error('Failed to begin transaction', { error });
      throw new Error('Failed to begin transaction');
    }
  }

  /**
   * Commits the current transaction
   */
  async commit(): Promise<void> {
    if (!this._transaction) {
      logger.warn('No active transaction to commit');
      return;
    }

    try {
      // Transaction is automatically committed when the callback completes
      this._transaction = null;
      logger.debug('Transaction committed');
    } catch (error) {
      logger.error('Failed to commit transaction', { error });
      throw new Error('Failed to commit transaction');
    }
  }

  /**
   * Rolls back the current transaction
   */
  async rollback(): Promise<void> {
    if (!this._transaction) {
      logger.warn('No active transaction to rollback');
      return;
    }

    try {
      // Transaction is automatically rolled back if an error is thrown
      this._transaction = null;
      logger.debug('Transaction rolled back');
    } catch (error) {
      logger.error('Failed to rollback transaction', { error });
      throw new Error('Failed to rollback transaction');
    }
  }

  /**
   * Executes a function within a transaction
   * Automatically commits on success or rolls back on error
   */
  async executeInTransaction<T>(operation: () => Promise<T>): Promise<T> {
    try {
      logger.debug('Starting transaction operation');

      const result = await this.prisma.$transaction(async (tx) => {
        // Store the transaction client for use in repositories
        this._transaction = tx;
        
        try {
          const operationResult = await operation();
          logger.debug('Transaction operation completed successfully');
          return operationResult;
        } catch (error) {
          logger.error('Transaction operation failed', { error });
          throw error;
        } finally {
          this._transaction = null;
        }
      });

      return result;
    } catch (error) {
      logger.error('Transaction failed', { error });
      throw error;
    }
  }

  /**
   * Releases database connections and cleans up resources
   */
  async dispose(): Promise<void> {
    try {
      if (this._transaction) {
        await this.rollback();
      }
      
      // Note: We don't disconnect the shared Prisma client here
      // as it might be used by other parts of the application
      logger.debug('Unit of work disposed');
    } catch (error) {
      logger.error('Failed to dispose unit of work', { error });
      throw new Error('Failed to dispose unit of work');
    }
  }

  /**
   * Gets the current transaction client if available
   */
  getTransactionClient(): Prisma.TransactionClient | PrismaClient {
    return this._transaction || this.prisma;
  }
}
