{"app": {"name": "Todo App", "description": "Manage your tasks efficiently"}, "navigation": {"dashboard": "Dashboard", "tasks": "Tasks", "profile": "Profile", "settings": "Settings", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "username": "Username", "firstName": "First Name", "lastName": "Last Name", "language": "Language", "loginTitle": "Welcome Back", "loginSubtitle": "Sign in to your account", "registerTitle": "Create Account", "registerSubtitle": "Join us today", "forgotPassword": "Forgot Password?", "rememberMe": "Remember me", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "loginSuccess": "Login successful", "registerSuccess": "Registration successful", "logoutSuccess": "Logout successful", "invalidCredentials": "Invalid email or password", "emailRequired": "Email is required", "passwordRequired": "Password is required", "usernameRequired": "Username is required", "passwordMinLength": "Password must be at least 8 characters", "emailInvalid": "Please enter a valid email address"}, "tasks": {"title": "Tasks", "createTask": "Create Task", "editTask": "Edit Task", "deleteTask": "Delete Task", "taskTitle": "Task Title", "taskDescription": "Description", "priority": "Priority", "dueDate": "Due Date", "status": "Status", "completed": "Completed", "pending": "Pending", "overdue": "Overdue", "dueSoon": "Due Soon", "all": "All", "search": "Search tasks...", "noTasks": "No tasks found", "createFirstTask": "Create your first task", "taskCreated": "Task created successfully", "taskUpdated": "Task updated successfully", "taskDeleted": "Task deleted successfully", "taskCompleted": "Task marked as completed", "taskUncompleted": "Task marked as pending", "confirmDelete": "Are you sure you want to delete this task?", "bulkActions": "Bulk Actions", "selectAll": "Select All", "markCompleted": "<PERSON> as Completed", "markPending": "<PERSON> as Pending", "deleteTasks": "Delete Tasks", "tasksSelected": "{count} task(s) selected"}, "priority": {"low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back, {name}!", "statistics": "Statistics", "totalTasks": "Total Tasks", "completedTasks": "Completed", "pendingTasks": "Pending", "overdueTasks": "Overdue", "recentTasks": "Recent Tasks", "upcomingTasks": "Upcoming Tasks", "quickActions": "Quick Actions"}, "profile": {"title": "Profile", "personalInfo": "Personal Information", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "updateProfile": "Update Profile", "profileUpdated": "Profile updated successfully", "passwordChanged": "Password changed successfully", "preferences": "Preferences", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System"}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "actions": "Actions", "filters": "Filters", "sortBy": "Sort by", "ascending": "Ascending", "descending": "Descending", "page": "Page", "of": "of", "items": "items", "noData": "No data available", "retry": "Retry", "refresh": "Refresh"}, "errors": {"generic": "Something went wrong. Please try again.", "network": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access denied.", "notFound": "The requested resource was not found.", "validation": "Please check your input and try again.", "server": "Server error. Please try again later."}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Must be at least {min} characters", "maxLength": "Must not exceed {max} characters", "pattern": "Invalid format", "numeric": "Must be a number", "positive": "Must be a positive number", "future": "Date must be in the future"}}