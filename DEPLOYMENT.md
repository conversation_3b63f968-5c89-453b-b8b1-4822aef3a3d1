# Deployment Guide

This guide covers deploying the Bilingual Todo Application to production environments.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   PostgreSQL    │
│   (Vue.js)      │───▶│   (Node.js)     │───▶│   Database      │
│   Port: 5173    │    │   Port: 3000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Production Deployment

### Backend Deployment

#### 1. Environment Setup
```bash
# Production environment variables
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# Database
DATABASE_URL="****************************************/todo_production"

# JWT Secrets (Generate strong secrets!)
JWT_SECRET="your-super-secure-jwt-secret-256-bits"
JWT_REFRESH_SECRET="your-super-secure-refresh-secret-256-bits"
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# CORS
CORS_ORIGIN="https://yourdomain.com,https://www.yourdomain.com"

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# API Documentation (disable in production)
API_DOCS_ENABLED=false
```

#### 2. Build and Deploy
```bash
# Build the application
npm run build

# Install production dependencies only
npm ci --only=production

# Run database migrations
npm run db:migrate:prod

# Start the application
npm start
```

#### 3. Process Management (PM2)
```bash
# Install PM2 globally
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'todo-api',
    script: 'dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: 'logs/err.log',
    out_file: 'logs/out.log',
    log_file: 'logs/combined.log',
    time: true
  }]
}
EOF

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Frontend Deployment

#### 1. Environment Setup
```bash
# Production environment variables
VITE_API_BASE_URL=https://api.yourdomain.com/api
VITE_API_TIMEOUT=10000
VITE_APP_NAME="Bilingual Todo App"
VITE_DEFAULT_LANGUAGE=en
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PWA=true
VITE_ENABLE_DEVTOOLS=false
```

#### 2. Build for Production
```bash
# Build the application
npm run build

# The dist/ folder contains the production build
```

#### 3. Static File Hosting
Deploy the `dist/` folder to any static hosting service:

**Nginx Configuration:**
```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    # SSL Configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.yourdomain.com;" always;
    
    # Serve static files
    root /var/www/todo-app/dist;
    index index.html;
    
    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API proxy (optional, if serving API from same domain)
    location /api/ {
        proxy_pass http://localhost:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🐳 Docker Deployment

### Backend Dockerfile
```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM node:18-alpine AS runtime

WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/prisma ./prisma

EXPOSE 3000

CMD ["npm", "start"]
```

### Frontend Dockerfile
```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine AS runtime

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose
```yaml
version: '3.8'

services:
  database:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: todo_production
      POSTGRES_USER: todo_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      NODE_ENV: production
      DATABASE_URL: ****************************************************/todo_production
      JWT_SECRET: your-super-secure-jwt-secret
      JWT_REFRESH_SECRET: your-super-secure-refresh-secret
    ports:
      - "3000:3000"
    depends_on:
      - database

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  postgres_data:
```

## 🔒 Security Considerations

### Backend Security
- Use strong JWT secrets (256-bit minimum)
- Enable HTTPS in production
- Configure CORS properly
- Implement rate limiting
- Use environment variables for secrets
- Enable request logging
- Regular security updates

### Frontend Security
- Configure CSP headers
- Enable HTTPS
- Sanitize user inputs
- Implement proper error handling
- Use secure authentication flow

### Database Security
- Use strong passwords
- Enable SSL connections
- Regular backups
- Access control
- Monitor for suspicious activity

## 📊 Monitoring and Logging

### Application Monitoring
- Use PM2 for process monitoring
- Implement health checks
- Monitor API response times
- Track error rates
- Set up alerts

### Log Management
- Centralized logging with ELK stack
- Log rotation
- Error tracking with Sentry
- Performance monitoring

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to server
        run: |
          # Your deployment script here
```

## 🚀 Performance Optimization

### Backend Optimization
- Enable gzip compression
- Use connection pooling
- Implement caching (Redis)
- Database query optimization
- CDN for static assets

### Frontend Optimization
- Code splitting
- Lazy loading
- Image optimization
- Bundle analysis
- Service worker for caching

## 📱 Mobile Considerations

The application is responsive and works on mobile devices. For native mobile apps, consider:
- Progressive Web App (PWA) features
- Capacitor for native app deployment
- Push notifications
- Offline functionality

## 🌍 Internationalization in Production

- Serve appropriate fonts for Arabic text
- Configure proper text direction (RTL/LTR)
- Use locale-specific date/number formatting
- Consider regional CDNs for better performance
