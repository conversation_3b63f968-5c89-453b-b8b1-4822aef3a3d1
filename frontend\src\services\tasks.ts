import { apiClient } from './api';
import type {
  Task,
  CreateTaskRequest,
  UpdateTaskRequest,
  TaskFilterOptions,
  PaginatedTaskResponse,
  TaskStatistics,
  ApiResponse
} from '@/types';

/**
 * Task service
 */
export class TaskService {
  /**
   * Create a new task
   */
  async createTask(taskData: CreateTaskRequest): Promise<Task> {
    const response = await apiClient.post<{ task: Task }>('/tasks', taskData);
    
    if (response.success && response.data) {
      return response.data.task;
    }
    
    throw new Error(response.message || 'Failed to create task');
  }

  /**
   * Get all tasks with filtering and pagination
   */
  async getTasks(filters?: TaskFilterOptions): Promise<PaginatedTaskResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, String(value));
        }
      });
    }
    
    const response = await apiClient.get<PaginatedTaskResponse>(
      `/tasks?${params.toString()}`
    );
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || 'Failed to get tasks');
  }

  /**
   * Get a specific task by ID
   */
  async getTaskById(taskId: string): Promise<Task> {
    const response = await apiClient.get<{ task: Task }>(`/tasks/${taskId}`);
    
    if (response.success && response.data) {
      return response.data.task;
    }
    
    throw new Error(response.message || 'Failed to get task');
  }

  /**
   * Update a task
   */
  async updateTask(taskId: string, updateData: UpdateTaskRequest): Promise<Task> {
    const response = await apiClient.put<{ task: Task }>(`/tasks/${taskId}`, updateData);
    
    if (response.success && response.data) {
      return response.data.task;
    }
    
    throw new Error(response.message || 'Failed to update task');
  }

  /**
   * Delete a task
   */
  async deleteTask(taskId: string): Promise<void> {
    const response = await apiClient.delete(`/tasks/${taskId}`);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete task');
    }
  }

  /**
   * Toggle task completion status
   */
  async toggleTaskCompletion(taskId: string): Promise<Task> {
    const response = await apiClient.patch<{ task: Task }>(`/tasks/${taskId}/toggle`);
    
    if (response.success && response.data) {
      return response.data.task;
    }
    
    throw new Error(response.message || 'Failed to toggle task completion');
  }

  /**
   * Get task statistics
   */
  async getTaskStatistics(): Promise<TaskStatistics> {
    const response = await apiClient.get<{ statistics: TaskStatistics }>('/tasks/statistics');
    
    if (response.success && response.data) {
      return response.data.statistics;
    }
    
    throw new Error(response.message || 'Failed to get task statistics');
  }

  /**
   * Bulk update tasks
   */
  async bulkUpdateTasks(
    taskIds: string[],
    updateData: Pick<UpdateTaskRequest, 'isCompleted' | 'priority'>
  ): Promise<Task[]> {
    const response = await apiClient.patch<{ tasks: Task[] }>('/tasks/bulk', {
      taskIds,
      ...updateData,
    });
    
    if (response.success && response.data) {
      return response.data.tasks;
    }
    
    throw new Error(response.message || 'Failed to bulk update tasks');
  }

  /**
   * Bulk delete tasks
   */
  async bulkDeleteTasks(taskIds: string[]): Promise<number> {
    const response = await apiClient.delete<{ deletedCount: number }>('/tasks/bulk', {
      data: { taskIds },
    });
    
    if (response.success && response.data) {
      return response.data.deletedCount;
    }
    
    throw new Error(response.message || 'Failed to bulk delete tasks');
  }

  /**
   * Search tasks
   */
  async searchTasks(query: string, filters?: Omit<TaskFilterOptions, 'search'>): Promise<Task[]> {
    const searchFilters: TaskFilterOptions = {
      ...filters,
      search: query,
    };
    
    const result = await this.getTasks(searchFilters);
    return result.tasks;
  }

  /**
   * Get overdue tasks
   */
  async getOverdueTasks(): Promise<Task[]> {
    const now = new Date();
    const filters: TaskFilterOptions = {
      isCompleted: false,
      dueDateTo: now.toISOString(),
      sortBy: 'dueDate',
      sortOrder: 'asc',
    };
    
    const result = await this.getTasks(filters);
    return result.tasks;
  }

  /**
   * Get tasks due soon (within next 7 days)
   */
  async getTasksDueSoon(): Promise<Task[]> {
    const now = new Date();
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    const filters: TaskFilterOptions = {
      isCompleted: false,
      dueDateFrom: now.toISOString(),
      dueDateTo: nextWeek.toISOString(),
      sortBy: 'dueDate',
      sortOrder: 'asc',
    };
    
    const result = await this.getTasks(filters);
    return result.tasks;
  }
}

// Create and export singleton instance
export const taskService = new TaskService();
