@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* Base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  html[dir="rtl"] {
    font-family: 'Noto Sans Arabic', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900 transition-colors duration-200;
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    body {
      @apply bg-gray-900 text-gray-100;
    }
  }
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
}
