import { Task, TaskPriority } from '../entities/Task';

/**
 * Task filter options for querying tasks
 */
export interface TaskFilterOptions {
  isCompleted?: boolean;
  priority?: TaskPriority;
  search?: string;
  dueDateFrom?: Date;
  dueDateTo?: Date;
  sortBy?: 'createdAt' | 'updatedAt' | 'dueDate' | 'priority' | 'title';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

/**
 * Data for creating a new task
 */
export interface CreateTaskData {
  title: string;
  description?: string;
  priority?: TaskPriority;
  dueDate?: Date;
  userId: string;
}

/**
 * Data for updating an existing task
 */
export interface UpdateTaskData {
  title?: string;
  description?: string;
  isCompleted?: boolean;
  priority?: TaskPriority;
  dueDate?: Date;
}

/**
 * Repository interface for task data access operations
 * Implements the Repository pattern for clean separation of concerns
 */
export interface ITaskRepository {
  /**
   * Creates a new task
   */
  create(data: CreateTaskData): Promise<Task>;

  /**
   * Finds a task by its ID
   */
  findById(id: string): Promise<Task | null>;

  /**
   * Finds all tasks for a specific user with optional filtering
   */
  findByUserId(userId: string, options?: TaskFilterOptions): Promise<Task[]>;

  /**
   * Updates an existing task
   */
  update(id: string, data: UpdateTaskData): Promise<Task | null>;

  /**
   * Deletes a task by its ID
   */
  delete(id: string): Promise<boolean>;

  /**
   * Gets the total count of tasks for a user with optional filtering
   */
  countByUserId(userId: string, options?: Omit<TaskFilterOptions, 'limit' | 'offset' | 'sortBy' | 'sortOrder'>): Promise<number>;

  /**
   * Finds overdue tasks for a specific user
   */
  findOverdueByUserId(userId: string): Promise<Task[]>;

  /**
   * Finds tasks due within a specific number of days
   */
  findDueSoonByUserId(userId: string, days: number): Promise<Task[]>;

  /**
   * Bulk updates multiple tasks
   */
  bulkUpdate(taskIds: string[], data: UpdateTaskData): Promise<Task[]>;

  /**
   * Bulk deletes multiple tasks
   */
  bulkDelete(taskIds: string[]): Promise<number>;
}
