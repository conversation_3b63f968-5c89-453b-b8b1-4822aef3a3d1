import { PrismaClient } from '@prisma/client';

/**
 * Test database setup and teardown
 */

let prisma: PrismaClient;

beforeAll(async () => {
  // Initialize test database connection
  prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL || 'postgresql://test:test@localhost:5432/todo_test',
      },
    },
  });

  await prisma.$connect();
});

afterAll(async () => {
  // Clean up database connection
  await prisma.$disconnect();
});

beforeEach(async () => {
  // Clean up database before each test
  await prisma.task.deleteMany();
  await prisma.user.deleteMany();
});

export { prisma };
