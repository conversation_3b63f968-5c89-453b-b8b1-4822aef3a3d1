/**
 * Priority levels for tasks
 */
export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

/**
 * Task domain entity representing a todo task
 */
export class Task {
  constructor(
    public readonly id: string,
    public readonly title: string,
    public readonly description: string | null,
    public readonly isCompleted: boolean,
    public readonly priority: TaskPriority,
    public readonly dueDate: Date | null,
    public readonly userId: string,
    public readonly createdAt: Date,
    public readonly updatedAt: Date
  ) {}

  /**
   * Checks if the task is completed
   */
  public isTaskCompleted(): boolean {
    return this.isCompleted;
  }

  /**
   * Checks if the task is overdue
   */
  public isOverdue(): boolean {
    if (!this.dueDate || this.isCompleted) {
      return false;
    }
    return new Date() > this.dueDate;
  }

  /**
   * Gets the number of days until due date
   */
  public getDaysUntilDue(): number | null {
    if (!this.dueDate) {
      return null;
    }
    const today = new Date();
    const diffTime = this.dueDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Checks if the task is high priority (HIGH or URGENT)
   */
  public isHighPriority(): boolean {
    return this.priority === TaskPriority.HIGH || this.priority === TaskPriority.URGENT;
  }

  /**
   * Gets priority level as number for sorting
   */
  public getPriorityLevel(): number {
    switch (this.priority) {
      case TaskPriority.LOW:
        return 1;
      case TaskPriority.MEDIUM:
        return 2;
      case TaskPriority.HIGH:
        return 3;
      case TaskPriority.URGENT:
        return 4;
      default:
        return 2;
    }
  }

  /**
   * Creates a Task instance from database data
   */
  public static fromDatabase(data: {
    id: string;
    title: string;
    description: string | null;
    isCompleted: boolean;
    priority: string;
    dueDate: Date | null;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
  }): Task {
    return new Task(
      data.id,
      data.title,
      data.description,
      data.isCompleted,
      data.priority as TaskPriority,
      data.dueDate,
      data.userId,
      data.createdAt,
      data.updatedAt
    );
  }

  /**
   * Creates a new task with updated completion status
   */
  public withCompletionStatus(isCompleted: boolean): Task {
    return new Task(
      this.id,
      this.title,
      this.description,
      isCompleted,
      this.priority,
      this.dueDate,
      this.userId,
      this.createdAt,
      new Date()
    );
  }
}
