import { Prisma } from '@prisma/client';
import { User } from '../../domain/entities/User';
import {
  IUserRepository,
  CreateUserData,
  UpdateUserData,
} from '../../domain/interfaces/IUserRepository';
import { prisma } from '../database/PrismaClient';
import { logger } from '../../shared/utils/logger';

/**
 * Prisma implementation of the User Repository
 * Handles all database operations for users
 */
export class PrismaUserRepository implements IUserRepository {
  /**
   * Creates a new user in the database
   */
  async create(data: CreateUserData): Promise<User> {
    try {
      const user = await prisma.user.create({
        data: {
          email: data.email,
          username: data.username,
          password: data.password,
          firstName: data.firstName || null,
          lastName: data.lastName || null,
          language: data.language || 'en',
        },
      });

      logger.info('User created', { userId: user.id, email: data.email });
      return User.fromDatabase(user);
    } catch (error) {
      logger.error('Failed to create user', { error, email: data.email });
      throw new Error('Failed to create user');
    }
  }

  /**
   * Finds a user by their ID
   */
  async findById(id: string): Promise<User | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
      });

      return user ? User.fromDatabase(user) : null;
    } catch (error) {
      logger.error('Failed to find user by ID', { error, id });
      throw new Error('Failed to find user');
    }
  }

  /**
   * Finds a user by their email address
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { email },
      });

      return user ? User.fromDatabase(user) : null;
    } catch (error) {
      logger.error('Failed to find user by email', { error, email });
      throw new Error('Failed to find user');
    }
  }

  /**
   * Finds a user by their username
   */
  async findByUsername(username: string): Promise<User | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { username },
      });

      return user ? User.fromDatabase(user) : null;
    } catch (error) {
      logger.error('Failed to find user by username', { error, username });
      throw new Error('Failed to find user');
    }
  }

  /**
   * Updates an existing user
   */
  async update(id: string, data: UpdateUserData): Promise<User | null> {
    try {
      const user = await prisma.user.update({
        where: { id },
        data: {
          ...(data.email !== undefined && { email: data.email }),
          ...(data.username !== undefined && { username: data.username }),
          ...(data.password !== undefined && { password: data.password }),
          ...(data.firstName !== undefined && { firstName: data.firstName }),
          ...(data.lastName !== undefined && { lastName: data.lastName }),
          ...(data.language !== undefined && { language: data.language }),
          ...(data.isActive !== undefined && { isActive: data.isActive }),
        },
      });

      logger.info('User updated', { userId: id });
      return User.fromDatabase(user);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return null;
      }
      logger.error('Failed to update user', { error, id });
      throw new Error('Failed to update user');
    }
  }

  /**
   * Deletes a user by their ID
   */
  async delete(id: string): Promise<boolean> {
    try {
      await prisma.user.delete({
        where: { id },
      });

      logger.info('User deleted', { userId: id });
      return true;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return false;
      }
      logger.error('Failed to delete user', { error, id });
      throw new Error('Failed to delete user');
    }
  }

  /**
   * Checks if an email address is already taken
   */
  async isEmailTaken(email: string, excludeUserId?: string): Promise<boolean> {
    try {
      const where: Prisma.UserWhereInput = { email };
      if (excludeUserId) {
        where.id = { not: excludeUserId };
      }

      const user = await prisma.user.findFirst({ where });
      return user !== null;
    } catch (error) {
      logger.error('Failed to check if email is taken', { error, email });
      throw new Error('Failed to check email availability');
    }
  }

  /**
   * Checks if a username is already taken
   */
  async isUsernameTaken(username: string, excludeUserId?: string): Promise<boolean> {
    try {
      const where: Prisma.UserWhereInput = { username };
      if (excludeUserId) {
        where.id = { not: excludeUserId };
      }

      const user = await prisma.user.findFirst({ where });
      return user !== null;
    } catch (error) {
      logger.error('Failed to check if username is taken', { error, username });
      throw new Error('Failed to check username availability');
    }
  }

  /**
   * Finds all active users (for admin purposes)
   */
  async findAllActive(): Promise<User[]> {
    try {
      const users = await prisma.user.findMany({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' },
      });

      return users.map(user => User.fromDatabase(user));
    } catch (error) {
      logger.error('Failed to find active users', { error });
      throw new Error('Failed to find active users');
    }
  }

  /**
   * Deactivates a user account
   */
  async deactivate(id: string): Promise<boolean> {
    try {
      await prisma.user.update({
        where: { id },
        data: { isActive: false },
      });

      logger.info('User deactivated', { userId: id });
      return true;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return false;
      }
      logger.error('Failed to deactivate user', { error, id });
      throw new Error('Failed to deactivate user');
    }
  }

  /**
   * Activates a user account
   */
  async activate(id: string): Promise<boolean> {
    try {
      await prisma.user.update({
        where: { id },
        data: { isActive: true },
      });

      logger.info('User activated', { userId: id });
      return true;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return false;
      }
      logger.error('Failed to activate user', { error, id });
      throw new Error('Failed to activate user');
    }
  }
}
