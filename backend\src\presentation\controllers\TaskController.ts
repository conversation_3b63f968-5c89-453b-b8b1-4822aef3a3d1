import { Request, Response } from 'express';
import { TaskService } from '../../application/services/TaskService';
import { TaskPriority } from '../../domain/entities/Task';
import { logger } from '../../shared/utils/logger';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';

/**
 * Task controller handling task-related HTTP requests
 */
export class TaskController {
  constructor(private readonly taskService: TaskService) {}

  /**
   * Create a new task
   * POST /api/tasks
   */
  createTask = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { title, description, priority, dueDate } = req.body;
    const userId = req.user!.id;

    const task = await this.taskService.createTask(userId, {
      title,
      description,
      priority: priority as TaskPriority,
      dueDate: dueDate ? new Date(dueDate) : undefined,
    });

    logger.info('Task created via API', { taskId: task.id, userId });

    res.status(201).json({
      success: true,
      message: 'Task created successfully',
      data: { task },
    });
  });

  /**
   * Get all tasks for the authenticated user
   * GET /api/tasks
   */
  getTasks = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const userId = req.user!.id;
    const query = req.query;

    // Parse query parameters
    const filterOptions = {
      isCompleted: query.isCompleted ? query.isCompleted === 'true' : undefined,
      priority: query.priority as TaskPriority,
      search: query.search as string,
      dueDateFrom: query.dueDateFrom ? new Date(query.dueDateFrom as string) : undefined,
      dueDateTo: query.dueDateTo ? new Date(query.dueDateTo as string) : undefined,
      sortBy: query.sortBy as any,
      sortOrder: query.sortOrder as 'asc' | 'desc',
      limit: query.limit ? parseInt(query.limit as string) : undefined,
      offset: query.offset ? parseInt(query.offset as string) : undefined,
    };

    const result = await this.taskService.getUserTasks(userId, filterOptions);

    res.status(200).json({
      success: true,
      message: 'Tasks retrieved successfully',
      data: result,
    });
  });

  /**
   * Get a specific task by ID
   * GET /api/tasks/:id
   */
  getTaskById = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const userId = req.user!.id;

    const task = await this.taskService.getTaskById(id, userId);

    res.status(200).json({
      success: true,
      message: 'Task retrieved successfully',
      data: { task },
    });
  });

  /**
   * Update a task
   * PUT /api/tasks/:id
   */
  updateTask = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const { title, description, isCompleted, priority, dueDate } = req.body;
    const userId = req.user!.id;

    const task = await this.taskService.updateTask(id, userId, {
      title,
      description,
      isCompleted,
      priority: priority as TaskPriority,
      dueDate: dueDate ? new Date(dueDate) : undefined,
    });

    logger.info('Task updated via API', { taskId: id, userId });

    res.status(200).json({
      success: true,
      message: 'Task updated successfully',
      data: { task },
    });
  });

  /**
   * Delete a task
   * DELETE /api/tasks/:id
   */
  deleteTask = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const userId = req.user!.id;

    await this.taskService.deleteTask(id, userId);

    logger.info('Task deleted via API', { taskId: id, userId });

    res.status(200).json({
      success: true,
      message: 'Task deleted successfully',
    });
  });

  /**
   * Toggle task completion status
   * PATCH /api/tasks/:id/toggle
   */
  toggleTaskCompletion = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { id } = req.params;
    const userId = req.user!.id;

    const task = await this.taskService.toggleTaskCompletion(id, userId);

    logger.info('Task completion toggled via API', { taskId: id, userId, isCompleted: task.isCompleted });

    res.status(200).json({
      success: true,
      message: 'Task completion status updated successfully',
      data: { task },
    });
  });

  /**
   * Get task statistics for the authenticated user
   * GET /api/tasks/statistics
   */
  getTaskStatistics = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const userId = req.user!.id;

    const statistics = await this.taskService.getTaskStatistics(userId);

    res.status(200).json({
      success: true,
      message: 'Task statistics retrieved successfully',
      data: { statistics },
    });
  });

  /**
   * Bulk update tasks
   * PATCH /api/tasks/bulk
   */
  bulkUpdateTasks = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { taskIds, isCompleted, priority } = req.body;
    const userId = req.user!.id;

    if (!taskIds || !Array.isArray(taskIds) || taskIds.length === 0) {
      res.status(400).json({
        success: false,
        message: 'Task IDs array is required',
        code: 'MISSING_TASK_IDS',
      });
      return;
    }

    const tasks = await this.taskService.bulkUpdateTasks(taskIds, userId, {
      isCompleted,
      priority: priority as TaskPriority,
    });

    logger.info('Tasks bulk updated via API', { count: tasks.length, userId });

    res.status(200).json({
      success: true,
      message: 'Tasks updated successfully',
      data: { tasks },
    });
  });

  /**
   * Bulk delete tasks
   * DELETE /api/tasks/bulk
   */
  bulkDeleteTasks = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { taskIds } = req.body;
    const userId = req.user!.id;

    if (!taskIds || !Array.isArray(taskIds) || taskIds.length === 0) {
      res.status(400).json({
        success: false,
        message: 'Task IDs array is required',
        code: 'MISSING_TASK_IDS',
      });
      return;
    }

    const deletedCount = await this.taskService.bulkDeleteTasks(taskIds, userId);

    logger.info('Tasks bulk deleted via API', { count: deletedCount, userId });

    res.status(200).json({
      success: true,
      message: 'Tasks deleted successfully',
      data: { deletedCount },
    });
  });
}
