import { Router } from 'express';
import { TaskController } from '../controllers/TaskController';
import { TaskService } from '../../application/services/TaskService';
import { AuthService } from '../../application/services/AuthService';
import { PrismaTaskRepository } from '../../infrastructure/repositories/PrismaTaskRepository';
import { PrismaUserRepository } from '../../infrastructure/repositories/PrismaUserRepository';
import { PrismaUnitOfWork } from '../../infrastructure/database/PrismaUnitOfWork';
import { prisma } from '../../infrastructure/database/PrismaClient';
import { validateBody, validateQuery, validateParams } from '../middleware/validation';
import { createAuthMiddleware } from '../middleware/auth';
import {
  taskCreationSchema,
  taskUpdateSchema,
  taskQuerySchema,
  idParamSchema,
} from '../../shared/utils/validation';
import Joi from 'joi';

/**
 * Bulk operations validation schemas
 */
const bulkUpdateSchema = Joi.object({
  taskIds: Joi.array()
    .items(Joi.string().required())
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one task ID is required',
      'any.required': 'Task IDs array is required',
    }),
  isCompleted: Joi.boolean().optional(),
  priority: Joi.string()
    .valid('LOW', 'MEDIUM', 'HIGH', 'URGENT')
    .optional()
    .messages({
      'any.only': 'Priority must be one of: LOW, MEDIUM, HIGH, URGENT',
    }),
});

const bulkDeleteSchema = Joi.object({
  taskIds: Joi.array()
    .items(Joi.string().required())
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one task ID is required',
      'any.required': 'Task IDs array is required',
    }),
});

/**
 * Creates task routes
 */
export const createTaskRoutes = (): Router => {
  const router = Router();
  
  // Initialize dependencies
  const userRepository = new PrismaUserRepository();
  const taskRepository = new PrismaTaskRepository();
  const unitOfWork = new PrismaUnitOfWork(prisma);
  const authService = new AuthService(userRepository);
  const taskService = new TaskService(taskRepository, unitOfWork);
  const taskController = new TaskController(taskService);
  const authMiddleware = createAuthMiddleware(authService);

  // Apply authentication middleware to all task routes
  router.use(authMiddleware);

  /**
   * @swagger
   * /api/tasks:
   *   post:
   *     summary: Create a new task
   *     tags: [Tasks]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - title
   *             properties:
   *               title:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 200
   *               description:
   *                 type: string
   *                 maxLength: 1000
   *               priority:
   *                 type: string
   *                 enum: [LOW, MEDIUM, HIGH, URGENT]
   *                 default: MEDIUM
   *               dueDate:
   *                 type: string
   *                 format: date-time
   *     responses:
   *       201:
   *         description: Task created successfully
   *       400:
   *         description: Validation error
   *       401:
   *         description: Authentication required
   */
  router.post('/', validateBody(taskCreationSchema), taskController.createTask);

  /**
   * @swagger
   * /api/tasks:
   *   get:
   *     summary: Get all tasks for the authenticated user
   *     tags: [Tasks]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: isCompleted
   *         schema:
   *           type: boolean
   *         description: Filter by completion status
   *       - in: query
   *         name: priority
   *         schema:
   *           type: string
   *           enum: [LOW, MEDIUM, HIGH, URGENT]
   *         description: Filter by priority
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *           maxLength: 100
   *         description: Search in title and description
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *           enum: [createdAt, updatedAt, dueDate, priority, title]
   *           default: createdAt
   *         description: Sort field
   *       - in: query
   *         name: sortOrder
   *         schema:
   *           type: string
   *           enum: [asc, desc]
   *           default: desc
   *         description: Sort order
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *         description: Number of tasks per page
   *       - in: query
   *         name: offset
   *         schema:
   *           type: integer
   *           minimum: 0
   *           default: 0
   *         description: Number of tasks to skip
   *     responses:
   *       200:
   *         description: Tasks retrieved successfully
   *       401:
   *         description: Authentication required
   */
  router.get('/', validateQuery(taskQuerySchema), taskController.getTasks);

  /**
   * @swagger
   * /api/tasks/statistics:
   *   get:
   *     summary: Get task statistics for the authenticated user
   *     tags: [Tasks]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Task statistics retrieved successfully
   *       401:
   *         description: Authentication required
   */
  router.get('/statistics', taskController.getTaskStatistics);

  /**
   * @swagger
   * /api/tasks/bulk:
   *   patch:
   *     summary: Bulk update tasks
   *     tags: [Tasks]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - taskIds
   *             properties:
   *               taskIds:
   *                 type: array
   *                 items:
   *                   type: string
   *                 minItems: 1
   *               isCompleted:
   *                 type: boolean
   *               priority:
   *                 type: string
   *                 enum: [LOW, MEDIUM, HIGH, URGENT]
   *     responses:
   *       200:
   *         description: Tasks updated successfully
   *       400:
   *         description: Validation error
   *       401:
   *         description: Authentication required
   */
  router.patch('/bulk', validateBody(bulkUpdateSchema), taskController.bulkUpdateTasks);

  /**
   * @swagger
   * /api/tasks/bulk:
   *   delete:
   *     summary: Bulk delete tasks
   *     tags: [Tasks]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - taskIds
   *             properties:
   *               taskIds:
   *                 type: array
   *                 items:
   *                   type: string
   *                 minItems: 1
   *     responses:
   *       200:
   *         description: Tasks deleted successfully
   *       400:
   *         description: Validation error
   *       401:
   *         description: Authentication required
   */
  router.delete('/bulk', validateBody(bulkDeleteSchema), taskController.bulkDeleteTasks);

  /**
   * @swagger
   * /api/tasks/{id}:
   *   get:
   *     summary: Get a specific task by ID
   *     tags: [Tasks]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Task ID
   *     responses:
   *       200:
   *         description: Task retrieved successfully
   *       401:
   *         description: Authentication required
   *       404:
   *         description: Task not found
   */
  router.get('/:id', validateParams(idParamSchema), taskController.getTaskById);

  /**
   * @swagger
   * /api/tasks/{id}:
   *   put:
   *     summary: Update a task
   *     tags: [Tasks]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Task ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               title:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 200
   *               description:
   *                 type: string
   *                 maxLength: 1000
   *               isCompleted:
   *                 type: boolean
   *               priority:
   *                 type: string
   *                 enum: [LOW, MEDIUM, HIGH, URGENT]
   *               dueDate:
   *                 type: string
   *                 format: date-time
   *     responses:
   *       200:
   *         description: Task updated successfully
   *       400:
   *         description: Validation error
   *       401:
   *         description: Authentication required
   *       404:
   *         description: Task not found
   */
  router.put('/:id', validateParams(idParamSchema), validateBody(taskUpdateSchema), taskController.updateTask);

  /**
   * @swagger
   * /api/tasks/{id}:
   *   delete:
   *     summary: Delete a task
   *     tags: [Tasks]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Task ID
   *     responses:
   *       200:
   *         description: Task deleted successfully
   *       401:
   *         description: Authentication required
   *       404:
   *         description: Task not found
   */
  router.delete('/:id', validateParams(idParamSchema), taskController.deleteTask);

  /**
   * @swagger
   * /api/tasks/{id}/toggle:
   *   patch:
   *     summary: Toggle task completion status
   *     tags: [Tasks]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Task ID
   *     responses:
   *       200:
   *         description: Task completion status updated successfully
   *       401:
   *         description: Authentication required
   *       404:
   *         description: Task not found
   */
  router.patch('/:id/toggle', validateParams(idParamSchema), taskController.toggleTaskCompletion);

  return router;
};
