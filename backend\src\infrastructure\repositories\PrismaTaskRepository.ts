import { Prisma } from '@prisma/client';
import { Task, TaskPriority } from '../../domain/entities/Task';
import {
  ITaskRepository,
  TaskFilterOptions,
  CreateTaskData,
  UpdateTaskData,
} from '../../domain/interfaces/ITaskRepository';
import { prisma } from '../database/PrismaClient';
import { logger } from '../../shared/utils/logger';

/**
 * Prisma implementation of the Task Repository
 * Handles all database operations for tasks
 */
export class PrismaTaskRepository implements ITaskRepository {
  /**
   * Creates a new task in the database
   */
  async create(data: CreateTaskData): Promise<Task> {
    try {
      const task = await prisma.task.create({
        data: {
          title: data.title,
          description: data.description || null,
          priority: data.priority || TaskPriority.MEDIUM,
          dueDate: data.dueDate || null,
          userId: data.userId,
        },
      });

      logger.info('Task created', { taskId: task.id, userId: data.userId });
      return Task.fromDatabase(task);
    } catch (error) {
      logger.error('Failed to create task', { error, data });
      throw new Error('Failed to create task');
    }
  }

  /**
   * Finds a task by its ID
   */
  async findById(id: string): Promise<Task | null> {
    try {
      const task = await prisma.task.findUnique({
        where: { id },
      });

      return task ? Task.fromDatabase(task) : null;
    } catch (error) {
      logger.error('Failed to find task by ID', { error, id });
      throw new Error('Failed to find task');
    }
  }

  /**
   * Finds all tasks for a specific user with optional filtering
   */
  async findByUserId(userId: string, options: TaskFilterOptions = {}): Promise<Task[]> {
    try {
      const where = this.buildWhereClause(userId, options);
      const orderBy = this.buildOrderByClause(options);

      const tasks = await prisma.task.findMany({
        where,
        orderBy,
        take: options.limit,
        skip: options.offset,
      });

      return tasks.map(task => Task.fromDatabase(task));
    } catch (error) {
      logger.error('Failed to find tasks by user ID', { error, userId, options });
      throw new Error('Failed to find tasks');
    }
  }

  /**
   * Updates an existing task
   */
  async update(id: string, data: UpdateTaskData): Promise<Task | null> {
    try {
      const task = await prisma.task.update({
        where: { id },
        data: {
          ...(data.title !== undefined && { title: data.title }),
          ...(data.description !== undefined && { description: data.description }),
          ...(data.isCompleted !== undefined && { isCompleted: data.isCompleted }),
          ...(data.priority !== undefined && { priority: data.priority }),
          ...(data.dueDate !== undefined && { dueDate: data.dueDate }),
        },
      });

      logger.info('Task updated', { taskId: id });
      return Task.fromDatabase(task);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return null;
      }
      logger.error('Failed to update task', { error, id, data });
      throw new Error('Failed to update task');
    }
  }

  /**
   * Deletes a task by its ID
   */
  async delete(id: string): Promise<boolean> {
    try {
      await prisma.task.delete({
        where: { id },
      });

      logger.info('Task deleted', { taskId: id });
      return true;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return false;
      }
      logger.error('Failed to delete task', { error, id });
      throw new Error('Failed to delete task');
    }
  }

  /**
   * Gets the total count of tasks for a user with optional filtering
   */
  async countByUserId(
    userId: string,
    options: Omit<TaskFilterOptions, 'limit' | 'offset' | 'sortBy' | 'sortOrder'> = {}
  ): Promise<number> {
    try {
      const where = this.buildWhereClause(userId, options);
      return await prisma.task.count({ where });
    } catch (error) {
      logger.error('Failed to count tasks', { error, userId, options });
      throw new Error('Failed to count tasks');
    }
  }

  /**
   * Finds overdue tasks for a specific user
   */
  async findOverdueByUserId(userId: string): Promise<Task[]> {
    try {
      const tasks = await prisma.task.findMany({
        where: {
          userId,
          isCompleted: false,
          dueDate: {
            lt: new Date(),
          },
        },
        orderBy: { dueDate: 'asc' },
      });

      return tasks.map(task => Task.fromDatabase(task));
    } catch (error) {
      logger.error('Failed to find overdue tasks', { error, userId });
      throw new Error('Failed to find overdue tasks');
    }
  }

  /**
   * Finds tasks due within a specific number of days
   */
  async findDueSoonByUserId(userId: string, days: number): Promise<Task[]> {
    try {
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + days);

      const tasks = await prisma.task.findMany({
        where: {
          userId,
          isCompleted: false,
          dueDate: {
            gte: new Date(),
            lte: endDate,
          },
        },
        orderBy: { dueDate: 'asc' },
      });

      return tasks.map(task => Task.fromDatabase(task));
    } catch (error) {
      logger.error('Failed to find tasks due soon', { error, userId, days });
      throw new Error('Failed to find tasks due soon');
    }
  }

  /**
   * Bulk updates multiple tasks
   */
  async bulkUpdate(taskIds: string[], data: UpdateTaskData): Promise<Task[]> {
    try {
      await prisma.task.updateMany({
        where: { id: { in: taskIds } },
        data: {
          ...(data.isCompleted !== undefined && { isCompleted: data.isCompleted }),
          ...(data.priority !== undefined && { priority: data.priority }),
        },
      });

      const tasks = await prisma.task.findMany({
        where: { id: { in: taskIds } },
      });

      logger.info('Tasks bulk updated', { count: tasks.length });
      return tasks.map(task => Task.fromDatabase(task));
    } catch (error) {
      logger.error('Failed to bulk update tasks', { error, taskIds, data });
      throw new Error('Failed to bulk update tasks');
    }
  }

  /**
   * Bulk deletes multiple tasks
   */
  async bulkDelete(taskIds: string[]): Promise<number> {
    try {
      const result = await prisma.task.deleteMany({
        where: { id: { in: taskIds } },
      });

      logger.info('Tasks bulk deleted', { count: result.count });
      return result.count;
    } catch (error) {
      logger.error('Failed to bulk delete tasks', { error, taskIds });
      throw new Error('Failed to bulk delete tasks');
    }
  }

  /**
   * Builds the where clause for task queries
   */
  private buildWhereClause(userId: string, options: TaskFilterOptions): Prisma.TaskWhereInput {
    const where: Prisma.TaskWhereInput = { userId };

    if (options.isCompleted !== undefined) {
      where.isCompleted = options.isCompleted;
    }

    if (options.priority) {
      where.priority = options.priority;
    }

    if (options.search) {
      where.OR = [
        { title: { contains: options.search, mode: 'insensitive' } },
        { description: { contains: options.search, mode: 'insensitive' } },
      ];
    }

    if (options.dueDateFrom || options.dueDateTo) {
      where.dueDate = {};
      if (options.dueDateFrom) {
        where.dueDate.gte = options.dueDateFrom;
      }
      if (options.dueDateTo) {
        where.dueDate.lte = options.dueDateTo;
      }
    }

    return where;
  }

  /**
   * Builds the order by clause for task queries
   */
  private buildOrderByClause(options: TaskFilterOptions): Prisma.TaskOrderByWithRelationInput {
    const sortBy = options.sortBy || 'createdAt';
    const sortOrder = options.sortOrder || 'desc';

    return { [sortBy]: sortOrder };
  }
}
