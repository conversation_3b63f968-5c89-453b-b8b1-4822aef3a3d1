import jwt, { SignOptions } from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { logger } from './logger';

/**
 * JWT payload interface
 */
export interface JwtPayload {
  userId: string;
  email: string;
  username: string;
  language: 'en' | 'ar';
}

/**
 * Token pair interface
 */
export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

/**
 * Authentication utility class
 */
export class AuthUtils {
  private static readonly JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret';
  private static readonly JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret';
  private static readonly JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
  private static readonly JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '30d';
  private static readonly BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12');

  /**
   * Hashes a password using bcrypt
   */
  static async hashPassword(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, this.BCRYPT_ROUNDS);
    } catch (error) {
      logger.error('Failed to hash password', { error: error instanceof Error ? error.message : String(error) });
      throw new Error('Failed to hash password');
    }
  }

  /**
   * Compares a plain text password with a hashed password
   */
  static async comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      logger.error('Failed to compare password', { error: error instanceof Error ? error.message : String(error) });
      throw new Error('Failed to compare password');
    }
  }

  /**
   * Generates an access token
   */
  static generateAccessToken(payload: JwtPayload): string {
    try {
      const options: SignOptions = {
        expiresIn: this.JWT_EXPIRES_IN,
        issuer: 'todo-api',
        audience: 'todo-app',
      };
      return jwt.sign(payload as object, this.JWT_SECRET, options);
    } catch (error) {
      logger.error('Failed to generate access token', { error: error instanceof Error ? error.message : String(error) });
      throw new Error('Failed to generate access token');
    }
  }

  /**
   * Generates a refresh token
   */
  static generateRefreshToken(payload: JwtPayload): string {
    try {
      const options: SignOptions = {
        expiresIn: this.JWT_REFRESH_EXPIRES_IN,
        issuer: 'todo-api',
        audience: 'todo-app',
      };
      return jwt.sign(payload as object, this.JWT_REFRESH_SECRET, options);
    } catch (error) {
      logger.error('Failed to generate refresh token', { error: error instanceof Error ? error.message : String(error) });
      throw new Error('Failed to generate refresh token');
    }
  }

  /**
   * Generates both access and refresh tokens
   */
  static generateTokenPair(payload: JwtPayload): TokenPair {
    return {
      accessToken: this.generateAccessToken(payload),
      refreshToken: this.generateRefreshToken(payload),
    };
  }

  /**
   * Verifies an access token
   */
  static verifyAccessToken(token: string): JwtPayload {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET, {
        issuer: 'todo-api',
        audience: 'todo-app',
      }) as JwtPayload;

      return decoded;
    } catch (error) {
      logger.error('Failed to verify access token', { error: error instanceof Error ? error.message : String(error) });
      throw new Error('Invalid or expired access token');
    }
  }

  /**
   * Verifies a refresh token
   */
  static verifyRefreshToken(token: string): JwtPayload {
    try {
      const decoded = jwt.verify(token, this.JWT_REFRESH_SECRET, {
        issuer: 'todo-api',
        audience: 'todo-app',
      }) as JwtPayload;

      return decoded;
    } catch (error) {
      logger.error('Failed to verify refresh token', { error: error instanceof Error ? error.message : String(error) });
      throw new Error('Invalid or expired refresh token');
    }
  }

  /**
   * Extracts token from Authorization header
   */
  static extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }

  /**
   * Gets the expiration date for a refresh token
   */
  static getRefreshTokenExpiration(): Date {
    const expiresIn = this.JWT_REFRESH_EXPIRES_IN;
    const now = new Date();
    
    // Parse the expiration string (e.g., "30d", "7h", "60m")
    const match = expiresIn.match(/^(\d+)([dhm])$/);
    if (!match) {
      // Default to 30 days if parsing fails
      return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    }

    const [, amount, unit] = match;
    const amountNum = parseInt(amount);

    switch (unit) {
      case 'd':
        return new Date(now.getTime() + amountNum * 24 * 60 * 60 * 1000);
      case 'h':
        return new Date(now.getTime() + amountNum * 60 * 60 * 1000);
      case 'm':
        return new Date(now.getTime() + amountNum * 60 * 1000);
      default:
        return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    }
  }
}
