import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import type { Language, Theme, NotificationState } from '@/types';

export const useUIStore = defineStore('ui', () => {
  // State
  const language = ref<Language>('en');
  const theme = ref<Theme>('system');
  const sidebarOpen = ref(false);
  const notifications = ref<NotificationState[]>([]);
  const isOnline = ref(navigator.onLine);

  // Getters
  const isRTL = computed(() => language.value === 'ar');
  const direction = computed(() => isRTL.value ? 'rtl' : 'ltr');
  const currentTheme = computed(() => {
    if (theme.value === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return theme.value;
  });

  // Actions
  function setLanguage(newLanguage: Language): void {
    language.value = newLanguage;
    localStorage.setItem('language', newLanguage);
    
    // Update document attributes
    document.documentElement.setAttribute('lang', newLanguage);
    document.documentElement.setAttribute('dir', newLanguage === 'ar' ? 'rtl' : 'ltr');
  }

  function setTheme(newTheme: Theme): void {
    theme.value = newTheme;
    localStorage.setItem('theme', newTheme);
    updateThemeClass();
  }

  function toggleSidebar(): void {
    sidebarOpen.value = !sidebarOpen.value;
  }

  function closeSidebar(): void {
    sidebarOpen.value = false;
  }

  function openSidebar(): void {
    sidebarOpen.value = true;
  }

  function addNotification(notification: Omit<NotificationState, 'id'>): string {
    const id = Date.now().toString();
    const newNotification: NotificationState = {
      ...notification,
      id,
      duration: notification.duration ?? 5000,
    };
    
    notifications.value.push(newNotification);
    
    // Auto-remove notification after duration (unless persistent)
    if (!newNotification.persistent && newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }
    
    return id;
  }

  function removeNotification(id: string): void {
    const index = notifications.value.findIndex(n => n.id === id);
    if (index > -1) {
      notifications.value.splice(index, 1);
    }
  }

  function clearNotifications(): void {
    notifications.value = [];
  }

  function showSuccess(title: string, message?: string): string {
    return addNotification({
      type: 'success',
      title,
      message,
    });
  }

  function showError(title: string, message?: string): string {
    return addNotification({
      type: 'error',
      title,
      message,
      duration: 8000, // Longer duration for errors
    });
  }

  function showWarning(title: string, message?: string): string {
    return addNotification({
      type: 'warning',
      title,
      message,
    });
  }

  function showInfo(title: string, message?: string): string {
    return addNotification({
      type: 'info',
      title,
      message,
    });
  }

  function updateOnlineStatus(): void {
    isOnline.value = navigator.onLine;
  }

  function updateThemeClass(): void {
    const root = document.documentElement;
    root.classList.remove('light', 'dark');
    
    if (currentTheme.value === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.add('light');
    }
  }

  // Initialize from localStorage
  function initialize(): void {
    // Load language
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && ['en', 'ar'].includes(savedLanguage)) {
      setLanguage(savedLanguage);
    } else {
      // Detect browser language
      const browserLang = navigator.language.split('-')[0];
      setLanguage(browserLang === 'ar' ? 'ar' : 'en');
    }

    // Load theme
    const savedTheme = localStorage.getItem('theme') as Theme;
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      setTheme(savedTheme);
    }

    // Listen for online/offline events
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
      if (theme.value === 'system') {
        updateThemeClass();
      }
    });
  }

  return {
    // State
    language,
    theme,
    sidebarOpen,
    notifications,
    isOnline,
    // Getters
    isRTL,
    direction,
    currentTheme,
    // Actions
    setLanguage,
    setTheme,
    toggleSidebar,
    closeSidebar,
    openSidebar,
    addNotification,
    removeNotification,
    clearNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    updateOnlineStatus,
    initialize,
  };
});
