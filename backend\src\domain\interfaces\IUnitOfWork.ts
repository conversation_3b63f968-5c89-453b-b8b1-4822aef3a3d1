import { ITaskRepository } from './ITaskRepository';
import { IUserRepository } from './IUserRepository';

/**
 * Unit of Work interface for coordinating multiple repository operations
 * Implements the Unit of Work pattern for transaction management
 */
export interface IUnitOfWork {
  /**
   * Task repository instance
   */
  readonly tasks: ITaskRepository;

  /**
   * User repository instance
   */
  readonly users: IUserRepository;

  /**
   * Begins a new database transaction
   */
  beginTransaction(): Promise<void>;

  /**
   * Commits the current transaction
   */
  commit(): Promise<void>;

  /**
   * Rolls back the current transaction
   */
  rollback(): Promise<void>;

  /**
   * Executes a function within a transaction
   * Automatically commits on success or rolls back on error
   */
  executeInTransaction<T>(operation: () => Promise<T>): Promise<T>;

  /**
   * Releases database connections and cleans up resources
   */
  dispose(): Promise<void>;
}
