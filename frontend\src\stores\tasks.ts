import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { taskService } from '@/services/tasks';
import type { 
  Task, 
  CreateTaskRequest, 
  UpdateTaskRequest, 
  TaskFilterOptions,
  TaskStatistics,
  TaskPriority 
} from '@/types';

export const useTaskStore = defineStore('tasks', () => {
  // State
  const tasks = ref<Task[]>([]);
  const statistics = ref<TaskStatistics | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const filters = ref<TaskFilterOptions>({
    sortBy: 'createdAt',
    sortOrder: 'desc',
    limit: 20,
    offset: 0,
  });
  const totalTasks = ref(0);
  const currentPage = ref(1);
  const totalPages = ref(0);

  // Getters
  const completedTasks = computed(() => 
    tasks.value.filter(task => task.isCompleted)
  );
  
  const pendingTasks = computed(() => 
    tasks.value.filter(task => !task.isCompleted)
  );
  
  const overdueTasks = computed(() => 
    tasks.value.filter(task => {
      if (task.isCompleted || !task.dueDate) return false;
      return new Date(task.dueDate) < new Date();
    })
  );
  
  const tasksDueSoon = computed(() => 
    tasks.value.filter(task => {
      if (task.isCompleted || !task.dueDate) return false;
      const dueDate = new Date(task.dueDate);
      const now = new Date();
      const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      return dueDate >= now && dueDate <= nextWeek;
    })
  );

  const tasksByPriority = computed(() => ({
    urgent: tasks.value.filter(task => task.priority === TaskPriority.URGENT),
    high: tasks.value.filter(task => task.priority === TaskPriority.HIGH),
    medium: tasks.value.filter(task => task.priority === TaskPriority.MEDIUM),
    low: tasks.value.filter(task => task.priority === TaskPriority.LOW),
  }));

  // Actions
  async function fetchTasks(newFilters?: Partial<TaskFilterOptions>): Promise<void> {
    isLoading.value = true;
    error.value = null;
    
    try {
      if (newFilters) {
        filters.value = { ...filters.value, ...newFilters };
      }
      
      const response = await taskService.getTasks(filters.value);
      tasks.value = response.tasks;
      totalTasks.value = response.total;
      currentPage.value = response.page;
      totalPages.value = response.totalPages;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch tasks';
    } finally {
      isLoading.value = false;
    }
  }

  async function createTask(taskData: CreateTaskRequest): Promise<Task> {
    isLoading.value = true;
    error.value = null;
    
    try {
      const newTask = await taskService.createTask(taskData);
      tasks.value.unshift(newTask);
      totalTasks.value++;
      return newTask;
    } catch (err: any) {
      error.value = err.message || 'Failed to create task';
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  async function updateTask(taskId: string, updateData: UpdateTaskRequest): Promise<Task> {
    isLoading.value = true;
    error.value = null;
    
    try {
      const updatedTask = await taskService.updateTask(taskId, updateData);
      const index = tasks.value.findIndex(task => task.id === taskId);
      if (index !== -1) {
        tasks.value[index] = updatedTask;
      }
      return updatedTask;
    } catch (err: any) {
      error.value = err.message || 'Failed to update task';
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  async function deleteTask(taskId: string): Promise<void> {
    isLoading.value = true;
    error.value = null;
    
    try {
      await taskService.deleteTask(taskId);
      tasks.value = tasks.value.filter(task => task.id !== taskId);
      totalTasks.value--;
    } catch (err: any) {
      error.value = err.message || 'Failed to delete task';
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  async function toggleTaskCompletion(taskId: string): Promise<void> {
    const task = tasks.value.find(t => t.id === taskId);
    if (!task) return;

    try {
      const updatedTask = await taskService.toggleTaskCompletion(taskId);
      const index = tasks.value.findIndex(t => t.id === taskId);
      if (index !== -1) {
        tasks.value[index] = updatedTask;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to toggle task completion';
      throw err;
    }
  }

  async function fetchStatistics(): Promise<void> {
    try {
      statistics.value = await taskService.getTaskStatistics();
    } catch (err: any) {
      console.warn('Failed to fetch statistics:', err);
    }
  }

  async function bulkUpdateTasks(
    taskIds: string[], 
    updateData: Pick<UpdateTaskRequest, 'isCompleted' | 'priority'>
  ): Promise<void> {
    isLoading.value = true;
    error.value = null;
    
    try {
      const updatedTasks = await taskService.bulkUpdateTasks(taskIds, updateData);
      
      // Update tasks in store
      updatedTasks.forEach(updatedTask => {
        const index = tasks.value.findIndex(task => task.id === updatedTask.id);
        if (index !== -1) {
          tasks.value[index] = updatedTask;
        }
      });
    } catch (err: any) {
      error.value = err.message || 'Failed to bulk update tasks';
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  async function bulkDeleteTasks(taskIds: string[]): Promise<void> {
    isLoading.value = true;
    error.value = null;
    
    try {
      await taskService.bulkDeleteTasks(taskIds);
      tasks.value = tasks.value.filter(task => !taskIds.includes(task.id));
      totalTasks.value -= taskIds.length;
    } catch (err: any) {
      error.value = err.message || 'Failed to bulk delete tasks';
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  function setFilters(newFilters: Partial<TaskFilterOptions>): void {
    filters.value = { ...filters.value, ...newFilters };
  }

  function clearFilters(): void {
    filters.value = {
      sortBy: 'createdAt',
      sortOrder: 'desc',
      limit: 20,
      offset: 0,
    };
  }

  function clearError(): void {
    error.value = null;
  }

  return {
    // State
    tasks,
    statistics,
    isLoading,
    error,
    filters,
    totalTasks,
    currentPage,
    totalPages,
    // Getters
    completedTasks,
    pendingTasks,
    overdueTasks,
    tasksDueSoon,
    tasksByPriority,
    // Actions
    fetchTasks,
    createTask,
    updateTask,
    deleteTask,
    toggleTaskCompletion,
    fetchStatistics,
    bulkUpdateTasks,
    bulkDeleteTasks,
    setFilters,
    clearFilters,
    clearError,
  };
});
