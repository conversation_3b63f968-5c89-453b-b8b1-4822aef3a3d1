# Bilingual To-Do Application

A modern, bilingual (Arabic/English) To-Do application built with Vue.js 3 frontend and Node.js backend, implementing Clean Architecture principles with Repository and Unit of Work patterns.

## 🚀 Features

### Frontend (Vue.js 3)
- ✅ Vue.js 3 with Composition API
- ✅ Pinia for state management
- ✅ Tailwind CSS for styling
- ✅ Vue I18n for Arabic/English internationalization
- ✅ RTL (Right-to-Left) support for Arabic
- ✅ Responsive design for mobile and desktop
- ✅ Task CRUD operations
- ✅ Task filtering and search
- ✅ TypeScript support

### Backend (Node.js)
- ✅ Node.js with Express.js framework
- ✅ PostgreSQL database with Prisma ORM
- ✅ JWT-based authentication
- ✅ Clean Architecture implementation
- ✅ Repository and Unit of Work patterns
- ✅ RESTful API with OpenAPI documentation
- ✅ Input validation with Joi
- ✅ Structured logging with Winston
- ✅ TypeScript support

## 🏗️ Architecture

This project follows Clean Architecture principles:

```
├── frontend/                 # Vue.js 3 application
│   ├── src/
│   │   ├── components/      # Reusable Vue components
│   │   ├── views/           # Page components
│   │   ├── stores/          # Pinia stores
│   │   ├── composables/     # Vue composables
│   │   ├── services/        # API services
│   │   ├── types/           # TypeScript types
│   │   ├── locales/         # i18n translations
│   │   └── utils/           # Utility functions
│   └── ...
├── backend/                 # Node.js API server
│   ├── src/
│   │   ├── domain/          # Domain entities and interfaces
│   │   ├── infrastructure/  # Database, external services
│   │   ├── application/     # Use cases and services
│   │   ├── presentation/    # Controllers and routes
│   │   └── shared/          # Shared utilities
│   └── ...
└── ...
```

## 🛠️ Tech Stack

### Frontend
- **Framework**: Vue.js 3 with Composition API
- **Build Tool**: Vite
- **State Management**: Pinia
- **Styling**: Tailwind CSS
- **Internationalization**: Vue I18n
- **HTTP Client**: Axios
- **Testing**: Vitest + Vue Test Utils
- **Type Checking**: TypeScript

### Backend
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: PostgreSQL
- **ORM**: Prisma
- **Authentication**: JWT
- **Validation**: Joi
- **Logging**: Winston
- **Testing**: Jest + Supertest
- **Documentation**: Swagger/OpenAPI
- **Type Checking**: TypeScript

## 🚀 Quick Start

### Prerequisites
- Node.js >= 18.0.0
- npm >= 9.0.0
- PostgreSQL >= 13

### Automated Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd bilingual-todo-app
```

2. Run the setup script:
```bash
chmod +x setup.sh
./setup.sh
```

### Manual Setup

1. Install root dependencies:
```bash
npm install
```

2. Setup backend:
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your database credentials
npx prisma generate
npx prisma migrate dev --name init
npx prisma db seed
cd ..
```

3. Setup frontend:
```bash
cd frontend
npm install
cp .env.example .env
cd ..
```

4. Start the development servers:
```bash
npm run dev
```

The application will be available at:
- Frontend: http://localhost:5173
- Backend API: http://localhost:3000
- API Documentation: http://localhost:3000/api-docs

### Default Test Accounts
After seeding the database:
- **English User**: <EMAIL> / password123
- **Arabic User**: <EMAIL> / password123

## 📝 Available Scripts

### Root Level
- `npm run dev` - Start both frontend and backend in development mode
- `npm run build` - Build both applications for production
- `npm run test` - Run tests for both applications
- `npm run lint` - Lint both applications
- `npm run format` - Format code with Prettier

### Backend Specific
- `npm run dev:backend` - Start backend development server
- `npm run build:backend` - Build backend for production
- `npm run test:backend` - Run backend tests

### Frontend Specific
- `npm run dev:frontend` - Start frontend development server
- `npm run build:frontend` - Build frontend for production
- `npm run test:frontend` - Run frontend tests

## 🌐 API Documentation

The API documentation is automatically generated using OpenAPI/Swagger and is available at:
- Development: http://localhost:3000/api-docs
- Production: https://your-domain.com/api-docs

## 🧪 Testing

### Backend Tests
```bash
cd backend
npm run test
npm run test:watch
npm run test:coverage
```

### Frontend Tests
```bash
cd frontend
npm run test
npm run test:watch
npm run test:coverage
```

## 🌍 Internationalization

The application supports Arabic and English languages with:
- RTL (Right-to-Left) layout for Arabic
- Proper text direction handling
- Date and number formatting
- Dynamic language switching

## 📦 Deployment

### Backend Deployment
1. Build the application: `npm run build:backend`
2. Set production environment variables
3. Run database migrations: `npm run db:migrate:prod`
4. Start the server: `npm start`

### Frontend Deployment
1. Build the application: `npm run build:frontend`
2. Deploy the `dist` folder to your hosting service

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
